<?php

declare(strict_types=1);

namespace App\Modules\Documents\Config;

class Group2bDocumentTypes
{
    public static function getAll(): array
    {
        return [
            // GROUP 2b: Financial and Social History Documents
            'mortgage_statement_uk' => [
                'name' => 'Mortgage Statement (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK mortgage statements accepted if dated within 12 months',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'lender_name' => [
                        'field_name' => 'lender_name',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Lender Name',
                        'placeholder' => 'Enter name of mortgage lender (bank or building society)',
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'issue_date' => [
                        'field_name' => 'issue_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Issue Date',
                        'placeholder' => 'Select issue date',
                        'validation_rules' => [
                            'required' => true,
                            'within_12_months' => true
                        ]
                    ],
                    'address_postcode' => [
                        'field_name' => 'address_postcode',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Address Postcode',
                        'placeholder' => 'Enter property postcode',
                        'validation_rules' => [
                            'required' => true,
                            'match_applicant_address' => true
                        ]
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ],

            'bank_building_society_statement_uk' => [
                'name' => 'Bank or Building Society Statement (UK / Non-UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK and non-UK bank statements with conditional fields',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'is_uk_bank' => [
                        'field_name' => 'is_uk_bank',
                        'field_type' => 'yes_no',
                        'required' => true,
                        'label' => 'Is this statement issued by a UK Bank?',
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'address_postcode_on_statement' => [
                        'field_name' => 'address_postcode_on_statement',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Address Postcode on statement',
                        'placeholder' => 'Enter postcode shown on statement',
                        'conditional' => true,
                        'conditional_field' => 'is_uk_bank',
                        'conditional_value' => 'no',
                        'validation_rules' => [
                            'required' => true,
                            'match_applicant_addresses' => true
                        ]
                    ],
                    'bank_country' => [
                        'field_name' => 'bank_country',
                        'field_type' => 'dropdown',
                        'required' => true,
                        'label' => 'Bank Country',
                        'placeholder' => 'Select bank country',
                        'options' => 'countries_list',
                        'conditional' => true,
                        'conditional_field' => 'is_uk_bank',
                        'conditional_value' => 'no',
                        'validation_rules' => [
                            'required' => true,
                            'match_address_history_country' => true
                        ]
                    ],
                    'bank_name' => [
                        'field_name' => 'bank_name',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Bank Name',
                        'placeholder' => 'Enter name of bank or building society',
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'account_number_last_4' => [
                        'field_name' => 'account_number_last_4',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Account Number (last 4 digits)',
                        'placeholder' => 'Enter last 4 digits of account number',
                        'validation_rules' => [
                            'required' => true,
                            'exactly_4_digits' => true
                        ]
                    ],
                    'statement_date' => [
                        'field_name' => 'statement_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Statement Date',
                        'placeholder' => 'Select statement date',
                        'validation_rules' => [
                            'required' => true,
                            'within_3_months' => true
                        ]
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ],

            'bank_building_society_account_opening_letter_uk' => [
                'name' => 'Bank/Building Society Account Opening Letter (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Must be UK bank and not a photocopy',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'is_uk_bank_not_photocopy' => [
                        'field_name' => 'is_uk_bank_not_photocopy',
                        'field_type' => 'yes_no',
                        'required' => true,
                        'label' => 'Is this statement issued by UK Bank and not a photocopy?',
                        'validation_rules' => [
                            'required' => true,
                            'must_be_yes' => true
                        ]
                    ],
                    'account_number' => [
                        'field_name' => 'account_number',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Account Number',
                        'placeholder' => 'Enter account number given',
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'issue_date' => [
                        'field_name' => 'issue_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Issue Date',
                        'placeholder' => 'Select issue date',
                        'validation_rules' => [
                            'required' => true,
                            'within_3_months' => true
                        ]
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ],

            'credit_card_statement_uk' => [
                'name' => 'Credit Card Statement (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Must be UK bank and not a photocopy',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'is_uk_bank_not_photocopy' => [
                        'field_name' => 'is_uk_bank_not_photocopy',
                        'field_type' => 'yes_no',
                        'required' => true,
                        'label' => 'Is this statement issued by UK Bank and not a photocopy?',
                        'validation_rules' => [
                            'required' => true,
                            'must_be_yes' => true
                        ]
                    ],
                    'account_number_last_4' => [
                        'field_name' => 'account_number_last_4',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Account Number (last 4 digits)',
                        'placeholder' => 'Enter last 4 digits of card/account',
                        'validation_rules' => [
                            'required' => true,
                            'exactly_4_digits' => true
                        ]
                    ],
                    'statement_date' => [
                        'field_name' => 'statement_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Statement Date',
                        'placeholder' => 'Select statement date',
                        'validation_rules' => [
                            'required' => true,
                            'within_3_months' => true
                        ]
                    ],
                    'address_postcode' => [
                        'field_name' => 'address_postcode',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Address Postcode',
                        'placeholder' => 'Enter address postcode',
                        'validation_rules' => [
                            'required' => true,
                            'match_applicant_address' => true
                        ]
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ],

            'financial_statement_pension_endowment_uk' => [
                'name' => 'Financial Statement (pension, endowment, etc - UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Recent financial statements must be within 12 months',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'issuer' => [
                        'field_name' => 'issuer',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Issuer',
                        'placeholder' => 'Enter issuer name',
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'issue_date' => [
                        'field_name' => 'issue_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Issue Date',
                        'placeholder' => 'Select issue date',
                        'validation_rules' => [
                            'required' => true,
                            'within_12_months' => true
                        ]
                    ],
                    'account_reference_number' => [
                        'field_name' => 'account_reference_number',
                        'field_type' => 'string',
                        'required' => false,
                        'label' => 'Account/Reference Number',
                        'placeholder' => 'Enter account number (if on statement)',
                        'validation_rules' => []
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ],

            'p45_p60_uk' => [
                'name' => 'P45 or P60 (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Only recently issued P45/P60 forms count',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'employer_name' => [
                        'field_name' => 'employer_name',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Employer Name',
                        'placeholder' => 'Enter who issued it (the employer)',
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'document_type_and_year' => [
                        'field_name' => 'document_type_and_year',
                        'field_type' => 'dropdown',
                        'required' => true,
                        'label' => 'Document Type and Year',
                        'placeholder' => 'Select document type',
                        'options' => ['P45', 'P60'],
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'issue_date' => [
                        'field_name' => 'issue_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Issue Date',
                        'placeholder' => 'Select when it was issued',
                        'validation_rules' => [
                            'required' => true,
                            'within_12_months' => true
                        ]
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ],

            'council_tax_statement_uk' => [
                'name' => 'Council Tax Statement (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Council tax bill within the last year is acceptable',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'council_name' => [
                        'field_name' => 'council_name',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Council Name',
                        'placeholder' => 'Enter which local council issued it',
                        'validation_rules' => [
                            'required' => true,
                            'must_be_uk_ci_council' => true
                        ]
                    ],
                    'issue_date' => [
                        'field_name' => 'issue_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Issue Date',
                        'placeholder' => 'Select date of statement',
                        'validation_rules' => [
                            'required' => true,
                            'within_12_months' => true
                        ]
                    ],
                    'property_postcode' => [
                        'field_name' => 'property_postcode',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Property Postcode',
                        'placeholder' => 'Enter property postcode',
                        'validation_rules' => [
                            'required' => true,
                            'match_applicant_address' => true
                        ]
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ],

            'letter_of_sponsorship_non_uk' => [
                'name' => 'Letter of Sponsorship (Non-UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Only used if applicant is abroad with UK sponsor. Hide if present address is UK',
                'validity_period' => 'Must be current',
                'data_fields' => [
                    'sponsor_name' => [
                        'field_name' => 'sponsor_name',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Sponsor Name',
                        'placeholder' => 'Enter name of UK employer providing the letter',
                        'validation_rules' => [
                            'required' => true,
                            'recognized_employer' => true
                        ]
                    ],
                    'validity' => [
                        'field_name' => 'validity',
                        'field_type' => 'string',
                        'required' => false,
                        'label' => 'Validity',
                        'placeholder' => 'Enter until when sponsorship is valid (if stated)',
                        'validation_rules' => [
                            'must_be_current' => true
                        ]
                    ],
                    'reference_number' => [
                        'field_name' => 'reference_number',
                        'field_type' => 'string',
                        'required' => false,
                        'label' => 'Reference Number',
                        'placeholder' => 'Enter reference or document number (if given)',
                        'validation_rules' => []
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_address' => ['UK'],
                    'show_for_address' => ['non-UK'],
                    'age_restrictions' => null
                ]
            ],

            'utility_bill_uk_not_mobile' => [
                'name' => 'Utility Bill (UK, Not Mobile Phone)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Recent UK utility bills accepted. Mobile phone bills excluded',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'provider_name' => [
                        'field_name' => 'provider_name',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Provider Name',
                        'placeholder' => 'Enter utility provider name',
                        'help_text' => 'Mobile phone bills are not accepted as utility bills.',
                        'validation_rules' => [
                            'required' => true,
                            'uk_utility_not_mobile' => true
                        ]
                    ],
                    'document_type' => [
                        'field_name' => 'document_type',
                        'field_type' => 'dropdown',
                        'required' => true,
                        'label' => 'Document Type',
                        'placeholder' => 'Select utility type',
                        'options' => ['Electricity', 'Gas', 'Water', 'Internet', 'Waste'],
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'issue_date' => [
                        'field_name' => 'issue_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Issue Date',
                        'placeholder' => 'Select date of bill',
                        'validation_rules' => [
                            'required' => true,
                            'within_3_months' => true
                        ]
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ],

            'benefit_statement_uk' => [
                'name' => 'Benefit Statement (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Recent benefit letters/statements verify identity/address',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'issuing_authority' => [
                        'field_name' => 'issuing_authority',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Issuing Authority',
                        'placeholder' => 'Enter issuing authority',
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'issue_date' => [
                        'field_name' => 'issue_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Issue Date',
                        'placeholder' => 'Select date of statement or letter',
                        'validation_rules' => [
                            'required' => true,
                            'within_3_months' => true
                        ]
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ],

            'government_entitlement_document_uk' => [
                'name' => 'Government Entitlement Document (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Letters confirming benefit eligibility must be recent',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'issuer' => [
                        'field_name' => 'issuer',
                        'field_type' => 'string',
                        'required' => true,
                        'label' => 'Issuer',
                        'placeholder' => 'Enter issuer name',
                        'validation_rules' => [
                            'required' => true
                        ]
                    ],
                    'issue_date' => [
                        'field_name' => 'issue_date',
                        'field_type' => 'date',
                        'required' => true,
                        'label' => 'Issue Date',
                        'placeholder' => 'Select date of letter',
                        'validation_rules' => [
                            'required' => true,
                            'within_3_months' => true
                        ]
                    ]
                ],
                'smart_filtering' => [
                    'hide_for_nationality' => [],
                    'show_for_nationality' => ['ANY'],
                    'age_restrictions' => null
                ]
            ]
        ];
    }
}
