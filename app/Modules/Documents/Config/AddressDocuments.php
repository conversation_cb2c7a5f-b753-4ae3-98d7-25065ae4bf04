<?php

declare(strict_types=1);

namespace App\Modules\Documents\Config;

class AddressDocuments
{
    public static function getAll(): array
    {
        return [
            'mortgage_statement_uk' => [
                'name' => 'Mortgage Statement (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK mortgage statements accepted if dated within 12 months.',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'lender_name' => ['type' => 'string', 'required' => true, 'label' => 'Lender Name'],
                    'account_reference_number' => ['type' => 'string', 'required' => true, 'label' => 'Account/Reference Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'property_address' => ['type' => 'string', 'required' => false, 'label' => 'Property Address']
                ]
            ],
            'bank_building_society_statement_uk_ci' => [
                'name' => 'Bank or Building Society Statement (UK/CI)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Printouts or online-only statements not allowed unless stamped by bank.',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'bank_name' => ['type' => 'string', 'required' => true, 'label' => 'Bank Name'],
                    'account_number_last_4_digits' => ['type' => 'string', 'required' => true, 'label' => 'Account Number (last 4 digits)'],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => 'Statement Date'],
                    'address_on_statement' => ['type' => 'string', 'required' => false, 'label' => 'Address on Statement']
                ]
            ],
            'bank_building_society_statement_non_uk' => [
                'name' => 'Bank or Building Society Statement (non-UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Foreign bank statements acceptable if within 3 months and issued in applicant\'s country of residence.',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'bank_name' => ['type' => 'string', 'required' => true, 'label' => 'Bank Name'],
                    'country_of_branch' => ['type' => 'string', 'required' => true, 'label' => 'Country of Branch'],
                    'account_number' => ['type' => 'string', 'required' => true, 'label' => 'Account Number'],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => 'Statement Date']
                ]
            ],
            'bank_building_society_account_opening_letter_uk' => [
                'name' => 'Bank/Building Society Account Opening Letter (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Confirmation letter showing account number and opening date must be recent.',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'bank_name' => ['type' => 'string', 'required' => true, 'label' => 'Bank Name'],
                    'account_number' => ['type' => 'string', 'required' => true, 'label' => 'Account Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date']
                ]
            ],
            'credit_card_statement_uk' => [
                'name' => 'Credit Card Statement (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Recent credit card statement verifies address and ID.',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'issuer_name' => ['type' => 'string', 'required' => true, 'label' => 'Issuer Name'],
                    'account_number_last_4_digits' => ['type' => 'string', 'required' => true, 'label' => 'Account Number (last 4 digits)'],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => 'Statement Date'],
                    'billing_address' => ['type' => 'string', 'required' => false, 'label' => 'Billing Address']
                ]
            ],
            'financial_statement_pension_endowment_uk' => [
                'name' => 'Financial Statement (pension, endowment, etc., UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Official financial statements must be recent.',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'provider_name' => ['type' => 'string', 'required' => true, 'label' => 'Provider Name'],
                    'type_of_statement' => ['type' => 'string', 'required' => true, 'label' => 'Type of Statement'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'account_reference_number' => ['type' => 'string', 'required' => false, 'label' => 'Account/Reference Number']
                ]
            ],
            'p45_p60_uk_ci' => [
                'name' => 'P45 or P60 (UK/CI)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Only recently issued P45/P60 forms count.',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'employer_name' => ['type' => 'string', 'required' => true, 'label' => 'Employer Name'],
                    'document_type_and_year' => ['type' => 'string', 'required' => true, 'label' => 'Document Type and Year'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date']
                ]
            ],
            'council_tax_statement_uk_ci' => [
                'name' => 'Council Tax Statement (UK/CI)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Council tax bill within the last year is acceptable.',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'council_name' => ['type' => 'string', 'required' => true, 'label' => 'Council Name'],
                    'tax_year' => ['type' => 'string', 'required' => true, 'label' => 'Tax Year'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'property_address' => ['type' => 'string', 'required' => true, 'label' => 'Property Address']
                ]
            ],
            'letter_of_sponsorship_future_uk_employer' => [
                'name' => 'Letter of Sponsorship from Future UK Employer (non-UK applicants)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Only used if applicant is abroad with UK sponsor. Letter must be current.',
                'validity_period' => 'Must be current',
                'data_fields' => [
                    'sponsor_name' => ['type' => 'string', 'required' => true, 'label' => 'Sponsor Name'],
                    'validity' => ['type' => 'string', 'required' => false, 'label' => 'Validity'],
                    'reference_number' => ['type' => 'string', 'required' => false, 'label' => 'Reference Number']
                ]
            ],
            'utility_bill_uk_not_mobile' => [
                'name' => 'Utility Bill (UK, not mobile phone)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Recent UK utility bills accepted. Mobile phone bills excluded.',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'provider_name' => ['type' => 'string', 'required' => true, 'label' => 'Provider Name'],
                    'account_holder_name' => ['type' => 'string', 'required' => true, 'label' => 'Account Holder Name'],
                    'service_address' => ['type' => 'string', 'required' => true, 'label' => 'Service Address'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date']
                ]
            ],
            'benefit_statement_uk' => [
                'name' => 'Benefit Statement (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Recent benefit letters/statements verify identity/address.',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'benefit_type' => ['type' => 'string', 'required' => true, 'label' => 'Benefit Type'],
                    'issuing_authority' => ['type' => 'string', 'required' => true, 'label' => 'Issuing Authority'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date']
                ]
            ],
            'government_entitlement_document_uk_ci' => [
                'name' => 'Government Entitlement Document (UK/CI)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Letters confirming benefit eligibility must be recent.',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'agency_name' => ['type' => 'string', 'required' => true, 'label' => 'Agency Name'],
                    'entitlement_type' => ['type' => 'string', 'required' => true, 'label' => 'Entitlement Type'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date']
                ]
            ],
            'hmrc_self_assessment_tax_demand_uk' => [
                'name' => 'HMRC Self-Assessment or Tax Demand Letter (UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'HMRC tax demand or SA statement must be recent.',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'reference_type' => ['type' => 'string', 'required' => true, 'label' => 'Reference/Type'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date']
                ]
            ],
            'ehic_ghic_uk' => [
                'name' => 'European Health Insurance Card (EHIC) or Global Health Insurance Card (GHIC, UK)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK EHIC/GHIC only valid up to expiry date.',
                'validity_period' => 'Must not be expired',
                'data_fields' => [
                    'card_number' => ['type' => 'string', 'required' => true, 'label' => 'Card Number'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date'],
                    'country_code' => ['type' => 'string', 'required' => true, 'label' => 'Country Code']
                ]
            ],
            'eea_national_id_card' => [
                'name' => 'EEA National ID Card',
                'document_group' => '2b',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Only valid unexpired EEA ID card accepted.',
                'validity_period' => 'Must not be expired',
                'data_fields' => [
                    'card_number' => ['type' => 'string', 'required' => true, 'label' => 'Card Number'],
                    'country_of_issue' => ['type' => 'string', 'required' => true, 'label' => 'Country of Issue'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date']
                ]
            ],
            'irish_passport_card' => [
                'name' => 'Irish Passport Card',
                'document_group' => '2b',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['IE'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Must be valid and not used alongside Irish passport.',
                'validity_period' => 'Must not be expired',
                'data_fields' => [
                    'card_number' => ['type' => 'string', 'required' => true, 'label' => 'Card Number'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date']
                ]
            ],
            'pass_id_card_uk_iom_ci' => [
                'name' => 'PASS ID Card (bearing PASS logo, UK/IoM/CI)',
                'document_group' => '2b',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'IM', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Digital PASS cards with QR code acceptable if validated. Must not be expired.',
                'validity_period' => 'Must not be expired',
                'data_fields' => [
                    'pass_number' => ['type' => 'string', 'required' => true, 'label' => 'PASS Number'],
                    'issuing_scheme' => ['type' => 'string', 'required' => true, 'label' => 'Issuing Scheme'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date']
                ]
            ],
            'letter_from_head_teacher_college_principal' => [
                'name' => 'Letter from Head Teacher / College Principal (for 16–19 year-olds)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Last-resort document for young applicants. Only used if no other documents available.',
                'validity_period' => 'Issued within 1 month',
                'data_fields' => [
                    'school_college_name' => ['type' => 'string', 'required' => true, 'label' => 'School/College Name'],
                    'date_of_letter' => ['type' => 'date', 'required' => true, 'label' => 'Date of Letter'],
                    'student_name' => ['type' => 'string', 'required' => true, 'label' => 'Student Name'],
                    'signature' => ['type' => 'string', 'required' => false, 'label' => 'Signature']
                ]
            ]
        ];
    }
}
