import 'package:flutter_test/flutter_test.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/document_validation_service.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/uk_driving_licence_validator.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/passport_validator.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/birth_certificate_validator.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/address_document_validator.dart';

void main() {
  group('UK Driving Licence Validation', () {
    test('validates UK driving licence number correctly', () {
      final result = UkDrivingLicenceValidator.validateLicenceNumber(
        licenceNumber: 'ROBIN751025CJ9901',
        surname: '<PERSON>',
        firstName: '<PERSON>',
        middleName: '<PERSON>',
        dateOfBirth: DateTime(1975, 7, 2),
        gender: 'female',
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('detects incorrect surname in licence number', () {
      final result = UkDrivingLicenceValidator.validateLicenceNumber(
        licenceNumber: 'SMITH751025CJ9901',
        surname: '<PERSON>',
        firstName: '<PERSON>',
        middleName: 'Josephine',
        dateOfBirth: DateTime(1975, 7, 2),
        gender: 'female',
      );

      expect(result.isValid, isFalse);
      expect(result.errors, contains('Surname in licence number does not match provided surname'));
    });

    test('validates female month encoding correctly', () {
      final result = UkDrivingLicenceValidator.validateLicenceNumber(
        licenceNumber: 'ROBIN756125CJ9901',
        surname: 'Robinson',
        firstName: 'Christine',
        middleName: 'Josephine',
        dateOfBirth: DateTime(1975, 11, 25),
        gender: 'female',
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('validates paper licence pre-2000 requirement', () {
      final result = UkDrivingLicenceValidator.validatePaperLicence(
        issueDate: DateTime(2001, 3, 1),
        expiryDate: DateTime(2030, 3, 1),
        dateOfBirth: DateTime(1975, 7, 2),
      );

      expect(result.isValid, isFalse);
      expect(result.errors, contains('Paper driving licence must be issued before March 2000 to be valid'));
    });
  });

  group('Passport Validation', () {
    test('validates UK passport number format', () {
      final result = PassportValidator.validatePassportNumber(
        passportNumber: '********9',
        countryOfIssue: 'UK',
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('rejects invalid UK passport number', () {
      final result = PassportValidator.validatePassportNumber(
        passportNumber: '********A',
        countryOfIssue: 'UK',
      );

      expect(result.isValid, isFalse);
      expect(result.errors, contains('UK passport number must contain only digits'));
    });

    test('validates UK passport expiry grace period', () {
      final expiredDate = DateTime.now().subtract(const Duration(days: 90));
      final result = PassportValidator.validatePassportDates(
        issueDate: DateTime.now().subtract(const Duration(days: 3650)),
        expiryDate: expiredDate,
        countryOfIssue: 'UK',
      );

      expect(result.isValid, isTrue);
      expect(result.warnings, contains('UK passport is expired but within 6-month grace period'));
    });

    test('rejects UK passport beyond grace period', () {
      final expiredDate = DateTime.now().subtract(const Duration(days: 200));
      final result = PassportValidator.validatePassportDates(
        issueDate: DateTime.now().subtract(const Duration(days: 3650)),
        expiryDate: expiredDate,
        countryOfIssue: 'UK',
      );

      expect(result.isValid, isFalse);
      expect(result.errors, contains('UK passport is expired beyond the 6-month grace period'));
    });

    test('validates e-passport chip requirement for recent UK passports', () {
      final result = PassportValidator.validateEPassportChip(
        hasEPassportChip: false,
        issueDate: DateTime(2010, 1, 1),
        countryOfIssue: 'UK',
      );

      expect(result.isValid, isTrue);
      expect(result.warnings, contains('UK passports issued after March 2006 should have e-passport chips'));
    });
  });

  group('Birth Certificate Validation', () {
    test('validates Group 1 birth certificate within 12 months', () {
      final birthDate = DateTime(1990, 5, 15);
      final registrationDate = DateTime(1990, 8, 10);
      
      final result = BirthCertificateValidator.validateGroup1BirthCertificate(
        dateOfBirth: birthDate,
        registrationDate: registrationDate,
        placeOfBirth: 'London, UK',
        certificateNumber: 'ABC123',
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('rejects Group 1 birth certificate registered after 12 months', () {
      final birthDate = DateTime(1990, 5, 15);
      final registrationDate = DateTime(1991, 8, 10);
      
      final result = BirthCertificateValidator.validateGroup1BirthCertificate(
        dateOfBirth: birthDate,
        registrationDate: registrationDate,
        placeOfBirth: 'London, UK',
        certificateNumber: 'ABC123',
      );

      expect(result.isValid, isFalse);
      expect(result.errors, contains('Birth certificate must be registered within 12 months of birth for Group 1 documents'));
    });

    test('validates UK embassy birth certificate', () {
      final result = BirthCertificateValidator.validateGroup1BirthCertificate(
        dateOfBirth: DateTime(1990, 5, 15),
        registrationDate: DateTime(1990, 8, 10),
        placeOfBirth: 'British Embassy, Paris',
        certificateNumber: 'ABC123',
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('validates adoption certificate', () {
      final result = BirthCertificateValidator.validateAdoptionCertificate(
        adoptionDate: DateTime(1995, 3, 20),
        issuingAuthority: 'Family Court, London',
        certificateNumber: 'ADOPT123',
        adoptedName: 'John Smith',
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });
  });

  group('Address Document Validation', () {
    test('validates recent mortgage statement', () {
      final result = AddressDocumentValidator.validateMortgageStatement(
        lenderName: 'Halifax',
        accountNumber: '********',
        issueDate: DateTime.now().subtract(const Duration(days: 30)),
        propertyAddress: '123 Main Street, London',
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('rejects old mortgage statement', () {
      final result = AddressDocumentValidator.validateMortgageStatement(
        lenderName: 'Halifax',
        accountNumber: '********',
        issueDate: DateTime.now().subtract(const Duration(days: 400)),
        propertyAddress: '123 Main Street, London',
      );

      expect(result.isValid, isFalse);
      expect(result.errors, contains('Mortgage statement must be issued within the last 12 months'));
    });

    test('validates recent utility bill', () {
      final result = AddressDocumentValidator.validateUtilityBill(
        providerName: 'British Gas',
        accountHolderName: 'John Smith',
        serviceAddress: '123 Main Street, London',
        issueDate: DateTime.now().subtract(const Duration(days: 30)),
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('rejects mobile phone bill as utility bill', () {
      final result = AddressDocumentValidator.validateUtilityBill(
        providerName: 'EE Mobile',
        accountHolderName: 'John Smith',
        serviceAddress: '123 Main Street, London',
        issueDate: DateTime.now().subtract(const Duration(days: 30)),
      );

      expect(result.isValid, isFalse);
      expect(result.errors, contains('Mobile phone bills are not accepted as utility bills'));
    });

    test('validates EHIC/GHIC card', () {
      final result = AddressDocumentValidator.validateEhicGhic(
        cardNumber: '***********',
        expiryDate: DateTime.now().add(const Duration(days: 365)),
        countryCode: 'UK',
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('rejects expired EHIC/GHIC card', () {
      final result = AddressDocumentValidator.validateEhicGhic(
        cardNumber: '***********',
        expiryDate: DateTime.now().subtract(const Duration(days: 30)),
        countryCode: 'UK',
      );

      expect(result.isValid, isFalse);
      expect(result.errors, contains('EHIC/GHIC card has expired'));
    });
  });

  group('Document Validation Service Integration', () {
    test('validates passport document data', () {
      final documentData = {
        'passport_number': '********9',
        'country_of_issue': 'UK',
        'nationality': 'British',
        'date_of_issue': '2020-01-01',
        'date_of_expiry': '2030-01-01',
        'e_passport_chip': true,
      };

      final result = DocumentValidationService.validateDocumentData(
        documentKey: 'passport_current',
        documentData: documentData,
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('validates UK driving licence with applicant data', () {
      final documentData = {
        'licence_number': 'ROBIN751025CJ9901',
        'date_of_issue': '2020-01-01',
        'date_of_expiry': '2030-01-01',
        'country_region_of_issue': 'UK',
      };

      final applicantData = {
        'surname': 'Robinson',
        'first_name': 'Christine',
        'middle_name': 'Josephine',
        'date_of_birth': '1975-07-02',
        'gender': 'female',
      };

      final result = DocumentValidationService.validateDocumentData(
        documentKey: 'driving_licence_uk_photocard',
        documentData: documentData,
        applicantData: applicantData,
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('validates mortgage statement', () {
      final documentData = {
        'lender_name': 'Halifax',
        'account_reference_number': '********',
        'issue_date': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        'property_address': '123 Main Street, London',
      };

      final result = DocumentValidationService.validateDocumentData(
        documentKey: 'mortgage_statement_uk',
        documentData: documentData,
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });
  });
}
