import 'package:flutter_test/flutter_test.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/comprehensive_document_validator.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/field_level_validation_service.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';

void main() {
  group('Comprehensive Document Validation', () {
    group('UK Driving Licence Validation', () {
      test('validates correct UK driving licence number format and encoding', () {
        final validationRules = {
          'uk_format': '16_characters',
          'encode_name_dob': true,
          'cross_check_applicant': true,
        };

        final applicantData = {
          'surname': '<PERSON>',
          'first_name': '<PERSON>',
          'middle_name': '<PERSON>',
          'date_of_birth': '1975-07-02',
          'gender': 'female',
        };

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'driving_licence_uk_photocard',
          fieldName: 'licence_number',
          fieldValue: 'ROBIN7507025CJ901',
          validationRules: validationRules,
          applicantData: applicantData,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('detects incorrect surname in licence number', () {
        final validationRules = {
          'uk_format': '16_characters',
          'encode_name_dob': true,
        };

        final applicantData = {
          'surname': 'Smith',
          'first_name': 'Christine',
          'date_of_birth': '1975-07-02',
          'gender': 'female',
        };

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'driving_licence_uk_photocard',
          fieldName: 'licence_number',
          fieldValue: 'SMITH7507025CJ901',
          validationRules: validationRules,
          applicantData: applicantData,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Surname in licence number does not match provided surname'));
      });

      test('validates female month encoding (+50)', () {
        final validationRules = {
          'uk_format': '16_characters',
          'encode_name_dob': true,
        };

        final applicantData = {
          'surname': 'Robinson',
          'date_of_birth': '1975-11-25',
          'gender': 'female',
        };

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'driving_licence_uk_photocard',
          fieldName: 'licence_number',
          fieldValue: 'ROBIN7561125CJ901',
          validationRules: validationRules,
          applicantData: applicantData,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });
    });

    group('Date Validation', () {
      test('rejects future dates when not_future rule is set', () {
        final futureDate = DateTime.now().add(const Duration(days: 30));
        final validationRules = {'not_future': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'passport_any',
          fieldName: 'date_of_issue',
          fieldValue: futureDate.toIso8601String(),
          validationRules: validationRules,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Date of Issue cannot be in the future'));
      });

      test('validates documents within timeframe', () {
        final recentDate = DateTime.now().subtract(const Duration(days: 30));
        final validationRules = {'within_3_months': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'utility_bill_uk_not_mobile',
          fieldName: 'issue_date',
          fieldValue: recentDate.toIso8601String(),
          validationRules: validationRules,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('rejects documents outside timeframe', () {
        final oldDate = DateTime.now().subtract(const Duration(days: 120));
        final validationRules = {'within_3_months': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'utility_bill_uk_not_mobile',
          fieldName: 'issue_date',
          fieldValue: oldDate.toIso8601String(),
          validationRules: validationRules,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Issue Date must be within the last 3 months'));
      });

      test('validates UK passport grace period', () {
        final expiredDate = DateTime.now().subtract(const Duration(days: 90));
        final validationRules = {'uk_grace_period': 180};
        final formData = {'country_of_issue': 'United Kingdom'};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'passport_any',
          fieldName: 'date_of_expiry',
          fieldValue: expiredDate.toIso8601String(),
          validationRules: validationRules,
          allFormData: formData,
        );

        expect(result.isValid, isTrue);
        expect(result.warnings, contains('UK passport is expired but within 6-month grace period'));
      });
    });

    group('Format Validation', () {
      test('validates BRP number format (12 digits)', () {
        final validationRules = {'format': '12_digits'};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'biometric_residence_permit_uk',
          fieldName: 'brp_number',
          fieldValue: '*********012',
          validationRules: validationRules,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('rejects invalid BRP number format', () {
        final validationRules = {'format': '12_digits'};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'biometric_residence_permit_uk',
          fieldName: 'brp_number',
          fieldValue: '12345',
          validationRules: validationRules,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, contains('BRP Number must be exactly 12 digits'));
      });

      test('validates exactly 4 digits for account numbers', () {
        final validationRules = {'exactly_4_digits': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'bank_building_society_statement_uk',
          fieldName: 'account_number_last_4',
          fieldValue: '1234',
          validationRules: validationRules,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });
    });

    group('Cross-Reference Validation', () {
      test('validates matching date of birth', () {
        final validationRules = {'match_applicant_dob': true};
        final applicantData = {'date_of_birth': '1990-05-15'};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'birth_certificate_12_months',
          fieldName: 'date_of_birth',
          fieldValue: '1990-05-15',
          validationRules: validationRules,
          applicantData: applicantData,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('detects mismatched date of birth', () {
        final validationRules = {'match_applicant_dob': true};
        final applicantData = {'date_of_birth': '1990-05-15'};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'birth_certificate_12_months',
          fieldName: 'date_of_birth',
          fieldValue: '1985-03-20',
          validationRules: validationRules,
          applicantData: applicantData,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Date of birth does not match applicant data'));
      });

      test('validates matching postcode', () {
        final validationRules = {'match_applicant_address': true};
        final applicantData = {
          'current_address': {'postcode': 'SW1A 1AA'}
        };

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'mortgage_statement_uk',
          fieldName: 'address_postcode',
          fieldValue: 'SW1A1AA',
          validationRules: validationRules,
          applicantData: applicantData,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });
    });

    group('Conditional Validation', () {
      test('validates must_be_yes requirement', () {
        final validationRules = {'must_be_yes': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'application_registration_card_uk',
          fieldName: 'has_valid_share_code',
          fieldValue: 'yes',
          validationRules: validationRules,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('rejects when must_be_yes is not satisfied', () {
        final validationRules = {'must_be_yes': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'application_registration_card_uk',
          fieldName: 'has_valid_share_code',
          fieldValue: 'no',
          validationRules: validationRules,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Valid Share Code/Verification Note must be Yes to proceed'));
      });

      test('validates paper licence before March 2000', () {
        final validationRules = {'before_march_2000': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'driving_licence_paper_uk_pre_2000',
          fieldName: 'issue_date',
          fieldValue: '1999-12-15',
          validationRules: validationRules,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('rejects paper licence after March 2000', () {
        final validationRules = {'before_march_2000': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'driving_licence_paper_uk_pre_2000',
          fieldName: 'issue_date',
          fieldValue: '2001-03-15',
          validationRules: validationRules,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Paper driving licence must be issued before March 2000 to be valid'));
      });
    });

    group('Special Validations', () {
      test('rejects mobile phone providers for utility bills', () {
        final validationRules = {'uk_utility_not_mobile': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'utility_bill_uk_not_mobile',
          fieldName: 'provider_name',
          fieldValue: 'EE Mobile',
          validationRules: validationRules,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Mobile phone bills are not accepted as utility bills'));
      });

      test('accepts valid utility providers', () {
        final validationRules = {'uk_utility_not_mobile': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'utility_bill_uk_not_mobile',
          fieldName: 'provider_name',
          fieldValue: 'British Gas',
          validationRules: validationRules,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('validates EEA countries', () {
        final validationRules = {'must_be_eea_country': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'eea_national_id_card',
          fieldName: 'country_of_issue',
          fieldValue: 'Germany',
          validationRules: validationRules,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('rejects non-EEA countries', () {
        final validationRules = {'must_be_eea_country': true};

        final result = ComprehensiveDocumentValidator.validateDocumentField(
          documentKey: 'eea_national_id_card',
          fieldName: 'country_of_issue',
          fieldValue: 'United States',
          validationRules: validationRules,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Country of Issue must be an EEA country'));
      });
    });
  });

  group('Field Level Validation Service', () {
    test('validates all fields in a document', () {
      final fields = [
        DocumentDataField(
          name: 'passport_number',
          type: 'string',
          required: true,
          label: 'Passport Number',
        ),
        DocumentDataField(
          name: 'date_of_expiry',
          type: 'date',
          required: true,
          label: 'Date of Expiry',
          validationRules: {'must_be_valid': true},
        ),
      ];

      final formData = {
        'passport_number': '*********',
        'date_of_expiry': DateTime.now().add(const Duration(days: 365)).toIso8601String(),
      };

      final result = FieldLevelValidationService.validateAllFields(
        fields: fields,
        formData: formData,
        documentKey: 'passport_any',
      );

      expect(result.isValid, isTrue);
      expect(result.errors, isEmpty);
    });

    test('detects missing required fields', () {
      final fields = [
        DocumentDataField(
          name: 'passport_number',
          type: 'string',
          required: true,
          label: 'Passport Number',
        ),
      ];

      final formData = <String, dynamic>{};

      final result = FieldLevelValidationService.validateAllFields(
        fields: fields,
        formData: formData,
        documentKey: 'passport_any',
      );

      expect(result.isValid, isFalse);
      expect(result.errors, contains('Passport Number is required'));
    });

    test('handles conditional field display', () {
      final field = DocumentDataField(
        name: 'conditional_field',
        type: 'string',
        required: true,
        label: 'Conditional Field',
        conditional: true,
        conditionalField: 'trigger_field',
        conditionalValue: 'yes',
      );

      // Field should not show when condition is not met
      final formData1 = {'trigger_field': 'no'};
      expect(FieldLevelValidationService.shouldShowField(field: field, formData: formData1), isFalse);

      // Field should show when condition is met
      final formData2 = {'trigger_field': 'yes'};
      expect(FieldLevelValidationService.shouldShowField(field: field, formData: formData2), isTrue);
    });
  });
}
