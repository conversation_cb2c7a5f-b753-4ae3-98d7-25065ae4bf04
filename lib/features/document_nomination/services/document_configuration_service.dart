import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';

class DocumentConfigurationService {
  static Future<List<DocumentType>> loadAllDocuments() async {
    // In production, this would make an API call to load from backend
    // For now, return the comprehensive document configurations
    return _getComprehensiveDocumentTypes();
  }

  static Future<List<DocumentType>> loadDocumentsForApplicant(ApplicantDetailsData applicantData) async {
    final allDocuments = await loadAllDocuments();
    
    // Apply smart filtering based on applicant data
    return allDocuments.where((document) {
      return _shouldShowDocumentForApplicant(document, applicantData);
    }).toList();
  }

  static bool _shouldShowDocumentForApplicant(DocumentType document, ApplicantDetailsData applicantData) {
    final smartFiltering = document.smartFiltering ?? {};
    
    // Check nationality-based filtering
    final nationality = applicantData.additionalApplicantDetails?.birthNationality?.toLowerCase() ?? '';
    final hideForNationality = smartFiltering['hide_for_nationality'] as List<String>? ?? [];
    final showForNationality = smartFiltering['show_for_nationality'] as List<String>? ?? ['ANY'];
    
    if (hideForNationality.contains(nationality) || hideForNationality.contains('non-UK') && nationality != 'british') {
      return false;
    }
    
    if (!showForNationality.contains('ANY') && !showForNationality.contains(nationality)) {
      return false;
    }
    
    // Check age-based filtering
    final ageRestrictions = smartFiltering['age_restrictions'] as List<int>?;
    if (ageRestrictions != null && ageRestrictions.length == 2) {
      final age = _calculateAge(DateTime.parse(applicantData.dateOfBirth));
      if (age < ageRestrictions[0] || age > ageRestrictions[1]) {
        return false;
      }
    }
    
    // Check address-based filtering
    final addressCountry = applicantData.currentAddress?.countryCode ?? 'GB';
    final hideForAddress = smartFiltering['hide_for_address'] as List<String>? ?? [];
    final showForAddress = smartFiltering['show_for_address'] as List<String>? ?? ['ANY'];
    
    if (hideForAddress.contains(addressCountry) || hideForAddress.contains('UK') && addressCountry == 'GB') {
      return false;
    }
    
    if (!showForAddress.contains('ANY') && !showForAddress.contains(addressCountry)) {
      return false;
    }
    
    return true;
  }

  static int _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  static List<DocumentType> _getComprehensiveDocumentTypes() {
    return [
      // GROUP 1: Primary Identity Documents
      DocumentType(
        key: 'passport_any',
        name: 'Passport (Any)',
        documentGroup: '1',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['ANY'],
        notes: 'UK passports may be accepted up to 6 months past expiry (grace period)',
        dataFields: [
          DocumentDataField(
            name: 'passport_number',
            type: 'string',
            required: true,
            label: 'Passport Number',
            placeholder: 'Enter passport number',
            validationRules: {
              'required': true,
              'format_check': true
            },
          ),
          DocumentDataField(
            name: 'country_of_issue',
            type: 'dropdown',
            required: true,
            label: 'Country of Issue',
            placeholder: 'Select country',
            options: _getCountriesList(),
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'nationality',
            type: 'dropdown',
            required: true,
            label: 'Nationality',
            placeholder: 'Select nationality',
            options: _getNationalitiesList(),
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'date_of_issue',
            type: 'date',
            required: true,
            label: 'Date of Issue',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'not_future': true
            },
          ),
          DocumentDataField(
            name: 'date_of_expiry',
            type: 'date',
            required: true,
            label: 'Date of Expiry',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'uk_grace_period': 180 // 6 months in days
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'e_visa_uk',
        name: 'e-Visa (UK)',
        documentGroup: '1',
        requiresPhoto: false,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'Hide this for UK Nationality',
        dataFields: [
          DocumentDataField(
            name: 'ukvi_share_code',
            type: 'string',
            required: true,
            label: 'UKVI Share Code',
            placeholder: 'Enter your general share code',
            helpText: 'Provide your general share code for UK immigration status',
            validationRules: {
              'required': true,
              'home_office_verification': true
            },
          ),
          DocumentDataField(
            name: 'date_of_issue',
            type: 'date',
            required: true,
            label: 'Date of Issue',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'not_future': true
            },
          ),
          DocumentDataField(
            name: 'date_of_expiry',
            type: 'date',
            required: true,
            label: 'Date of Expiry',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'must_be_valid': true
            },
          ),
          DocumentDataField(
            name: 'visa_reference_number',
            type: 'string',
            required: false,
            label: 'Visa/Reference Number',
            placeholder: 'Enter visa or permit number (if applicable)',
            validationRules: {
              'home_office_match': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': ['British', 'UK'],
          'show_for_nationality': ['non-UK'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'biometric_residence_permit_uk',
        name: 'Biometric Residence Permit (UK)',
        documentGroup: '1',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'Hide this for UK Nationality. BRP with indefinite leave accepted up to 18 months past expiry',
        dataFields: [
          DocumentDataField(
            name: 'brp_number',
            type: 'string',
            required: true,
            label: 'BRP Number',
            placeholder: 'Enter BRP card number (typically 12 digits)',
            validationRules: {
              'required': true,
              'format': '12_digits'
            },
          ),
          DocumentDataField(
            name: 'immigration_status',
            type: 'dropdown',
            required: true,
            label: 'Immigration Status/Type',
            placeholder: 'Select status',
            options: [
              'Indefinite Leave to Remain',
              'Indefinite Leave to Enter',
              'No Time Limit',
              'Other limited leave'
            ],
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'date_of_expiry',
            type: 'date',
            required: true,
            label: 'Date of Expiry',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'brp_expiry_rules': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': ['British', 'UK'],
          'show_for_nationality': ['non-UK'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'application_registration_card_uk',
        name: 'Application Registration Card (ARC, UK)',
        documentGroup: '1',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'Hide this for UK Nationality. Must have valid share code or positive verification note',
        dataFields: [
          DocumentDataField(
            name: 'arc_number',
            type: 'string',
            required: true,
            label: 'ARC Number',
            placeholder: 'Enter ARC number (usually starts with "ARC")',
            validationRules: {
              'required': true,
              'arc_format': true
            },
          ),
          DocumentDataField(
            name: 'date_of_issue',
            type: 'date',
            required: true,
            label: 'Date of Issue',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'not_future': true
            },
          ),
          DocumentDataField(
            name: 'has_valid_share_code',
            type: 'yes_no',
            required: true,
            label: 'Do you have Valid Share code or Positive Verification Note?',
            validationRules: {
              'required': true,
              'must_be_yes': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': ['British', 'UK'],
          'show_for_nationality': ['non-UK'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'driving_licence_uk_photocard',
        name: 'Photocard Driving Licence (UK)',
        documentGroup: '1',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'UK licence numbers encode name and DOB for verification',
        dataFields: [
          DocumentDataField(
            name: 'licence_number',
            type: 'string',
            required: true,
            label: 'Licence Number',
            placeholder: 'Enter full driving licence number (16 characters)',
            helpText: 'UK driving licence numbers encode your surname, date of birth, and gender. We will verify this matches your application details.',
            validationRules: {
              'required': true,
              'uk_format': '16_characters',
              'encode_name_dob': true,
              'cross_check_applicant': true
            },
          ),
          DocumentDataField(
            name: 'date_of_issue',
            type: 'date',
            required: true,
            label: 'Date of Issue',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'not_future': true
            },
          ),
          DocumentDataField(
            name: 'date_of_expiry',
            type: 'date',
            required: true,
            label: 'Date of Expiry',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'must_be_valid': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': null
        },
      ),

      // Add more Group 1 documents
      DocumentType(
        key: 'birth_certificate_12_months',
        name: 'Birth Certificate (issued ≤12 months after birth)',
        documentGroup: '1',
        requiresPhoto: false,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'Hide this for Non-UK Nationality. Must be registered within 12 months of birth',
        dataFields: [
          DocumentDataField(
            name: 'date_of_birth',
            type: 'date',
            required: true,
            label: 'Date of Birth',
            placeholder: 'Select date of birth as stated on certificate',
            validationRules: {
              'required': true,
              'match_applicant_dob': true
            },
          ),
          DocumentDataField(
            name: 'date_of_issue_registration',
            type: 'date',
            required: true,
            label: 'Date of Issue (Registration Date)',
            placeholder: 'Select registration date',
            helpText: 'For Group 1 documents, birth must be registered within 12 months of birth.',
            validationRules: {
              'required': true,
              'within_12_months_of_birth': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': ['non-UK'],
          'show_for_nationality': ['British', 'UK'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'adoption_certificate_uk',
        name: 'Adoption Certificate (UK)',
        documentGroup: '1',
        requiresPhoto: false,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'Hide this for Non-UK Nationality. Must be issued within UK',
        dataFields: [
          DocumentDataField(
            name: 'issued_within_uk',
            type: 'yes_no',
            required: true,
            label: 'Is this Document issued within the UK?',
            validationRules: {
              'required': true,
              'must_be_yes': true
            },
          ),
          DocumentDataField(
            name: 'date_of_adoption',
            type: 'date',
            required: true,
            label: 'Date of Adoption',
            placeholder: 'Select adoption date',
            validationRules: {
              'required': true,
              'plausible_age_check': true
            },
          ),
          DocumentDataField(
            name: 'adopted_full_name',
            type: 'string',
            required: false,
            label: 'Adopted Full Name',
            placeholder: 'Enter full name recorded on adoption certificate (if applicable)',
            validationRules: {
              'match_applicant_name': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': ['non-UK'],
          'show_for_nationality': ['British', 'UK'],
          'age_restrictions': null
        },
      ),

      // Group 2a documents
      DocumentType(
        key: 'driving_licence_non_uk_photocard',
        name: 'Photocard Driving Licence (Non-UK)',
        documentGroup: '2a',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['ANY'],
        notes: 'Non-UK photocard driving licence must be valid',
        dataFields: [
          DocumentDataField(
            name: 'licence_number',
            type: 'string',
            required: true,
            label: 'Licence Number',
            placeholder: 'Enter full licence number',
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'issue_date',
            type: 'date',
            required: true,
            label: 'Issue Date',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'not_future': true
            },
          ),
          DocumentDataField(
            name: 'expiry_date',
            type: 'date',
            required: true,
            label: 'Expiry Date',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'must_be_valid': true
            },
          ),
          DocumentDataField(
            name: 'country_of_issue',
            type: 'dropdown',
            required: true,
            label: 'Country of Issue',
            placeholder: 'Select country',
            options: _getCountriesListWithoutUk(),
            validationRules: {
              'required': true,
              'must_be_outside_uk': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'driving_licence_paper_uk_pre_2000',
        name: 'Paper Driving Licence (UK)',
        documentGroup: '2a',
        requiresPhoto: false,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'UK paper licence only valid if issued before March 2000',
        dataFields: [
          DocumentDataField(
            name: 'licence_number',
            type: 'string',
            required: true,
            label: 'Licence Number',
            placeholder: 'Enter licence number',
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'issue_date',
            type: 'date',
            required: true,
            label: 'Issue Date',
            placeholder: 'Select date',
            helpText: 'Paper driving licence must be issued before March 2000 to be valid.',
            validationRules: {
              'required': true,
              'before_march_2000': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': null
        },
      ),

      // Group 2b documents
      DocumentType(
        key: 'mortgage_statement_uk',
        name: 'Mortgage Statement (UK)',
        documentGroup: '2b',
        requiresPhoto: false,
        confirmsAddress: true,
        applicableCountries: ['GB', 'UK'],
        notes: 'UK mortgage statements accepted if dated within 12 months',
        dataFields: [
          DocumentDataField(
            name: 'lender_name',
            type: 'string',
            required: true,
            label: 'Lender Name',
            placeholder: 'Enter name of mortgage lender (bank or building society)',
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'issue_date',
            type: 'date',
            required: true,
            label: 'Issue Date',
            placeholder: 'Select issue date',
            validationRules: {
              'required': true,
              'within_12_months': true
            },
          ),
          DocumentDataField(
            name: 'address_postcode',
            type: 'string',
            required: true,
            label: 'Address Postcode',
            placeholder: 'Enter property postcode',
            validationRules: {
              'required': true,
              'match_applicant_address': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'bank_building_society_statement_uk',
        name: 'Bank or Building Society Statement (UK / Non-UK)',
        documentGroup: '2b',
        requiresPhoto: false,
        confirmsAddress: true,
        applicableCountries: ['ANY'],
        notes: 'UK and non-UK bank statements with conditional fields',
        dataFields: [
          DocumentDataField(
            name: 'is_uk_bank',
            type: 'yes_no',
            required: true,
            label: 'Is this statement issued by a UK Bank?',
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'address_postcode_on_statement',
            type: 'string',
            required: true,
            label: 'Address Postcode on statement',
            placeholder: 'Enter postcode shown on statement',
            conditional: true,
            conditionalField: 'is_uk_bank',
            conditionalValue: 'no',
            validationRules: {
              'required': true,
              'match_applicant_addresses': true
            },
          ),
          DocumentDataField(
            name: 'bank_country',
            type: 'dropdown',
            required: true,
            label: 'Bank Country',
            placeholder: 'Select bank country',
            options: _getCountriesList(),
            conditional: true,
            conditionalField: 'is_uk_bank',
            conditionalValue: 'no',
            validationRules: {
              'required': true,
              'match_address_history_country': true
            },
          ),
          DocumentDataField(
            name: 'bank_name',
            type: 'string',
            required: true,
            label: 'Bank Name',
            placeholder: 'Enter name of bank or building society',
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'account_number_last_4',
            type: 'string',
            required: true,
            label: 'Account Number (last 4 digits)',
            placeholder: 'Enter last 4 digits of account number',
            validationRules: {
              'required': true,
              'exactly_4_digits': true
            },
          ),
          DocumentDataField(
            name: 'statement_date',
            type: 'date',
            required: true,
            label: 'Statement Date',
            placeholder: 'Select statement date',
            validationRules: {
              'required': true,
              'within_3_months': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'utility_bill_uk_not_mobile',
        name: 'Utility Bill (UK, Not Mobile Phone)',
        documentGroup: '2b',
        requiresPhoto: false,
        confirmsAddress: true,
        applicableCountries: ['GB', 'UK'],
        notes: 'Recent UK utility bills accepted. Mobile phone bills excluded',
        dataFields: [
          DocumentDataField(
            name: 'provider_name',
            type: 'string',
            required: true,
            label: 'Provider Name',
            placeholder: 'Enter utility provider name',
            helpText: 'Mobile phone bills are not accepted as utility bills.',
            validationRules: {
              'required': true,
              'uk_utility_not_mobile': true
            },
          ),
          DocumentDataField(
            name: 'document_type',
            type: 'dropdown',
            required: true,
            label: 'Document Type',
            placeholder: 'Select utility type',
            options: ['Electricity', 'Gas', 'Water', 'Internet', 'Waste'],
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'issue_date',
            type: 'date',
            required: true,
            label: 'Issue Date',
            placeholder: 'Select date of bill',
            validationRules: {
              'required': true,
              'within_3_months': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'letter_from_head_teacher_college_principal',
        name: 'Letter from Head Teacher / College Principal (for 16–19 year-olds)',
        documentGroup: '2b',
        requiresPhoto: false,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'Last-resort document for young applicants. Must be current',
        dataFields: [
          DocumentDataField(
            name: 'school_college_name',
            type: 'string',
            required: true,
            label: 'School/College Name',
            placeholder: 'Enter which institution issued it',
            validationRules: {
              'required': true,
              'current_institution': true
            },
          ),
          DocumentDataField(
            name: 'date_of_letter',
            type: 'date',
            required: true,
            label: 'Date of Letter',
            placeholder: 'Select date',
            helpText: 'Letter must be very recent - issued within the last month.',
            validationRules: {
              'required': true,
              'within_1_month': true
            },
          ),
          DocumentDataField(
            name: 'student_name_matches',
            type: 'yes_no_confirmation',
            required: true,
            label: 'Does the name on the document match [APPLICANT_NAME_HERE]?',
            validationRules: {
              'required': true,
              'must_be_yes': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': [16, 19] // Only for 16-19 year olds
        },
      ),
    ];
  }

  static List<String> _getCountriesList() {
    return [
      'United Kingdom', 'Ireland', 'France', 'Germany', 'Spain', 'Italy',
      'Netherlands', 'Belgium', 'Portugal', 'Poland', 'Czech Republic',
      'Hungary', 'Romania', 'Bulgaria', 'Croatia', 'Slovenia', 'Slovakia',
      'Estonia', 'Latvia', 'Lithuania', 'Finland', 'Sweden', 'Denmark',
      'Austria', 'Greece', 'Cyprus', 'Malta', 'Luxembourg', 'United States',
      'Canada', 'Australia', 'New Zealand', 'South Africa', 'India',
      'Pakistan', 'Bangladesh', 'Nigeria', 'Ghana', 'Kenya', 'Other'
    ];
  }

  static List<String> _getCountriesListWithoutUk() {
    return _getCountriesList().where((country) =>
      !country.toLowerCase().contains('kingdom')).toList();
  }

  static List<String> _getNationalitiesList() {
    return [
      'British', 'Irish', 'French', 'German', 'Spanish', 'Italian',
      'Dutch', 'Belgian', 'Portuguese', 'Polish', 'Czech', 'Hungarian',
      'Romanian', 'Bulgarian', 'Croatian', 'Slovenian', 'Slovak',
      'Estonian', 'Latvian', 'Lithuanian', 'Finnish', 'Swedish', 'Danish',
      'Austrian', 'Greek', 'Cypriot', 'Maltese', 'Luxembourgish',
      'American', 'Canadian', 'Australian', 'New Zealand', 'South African',
      'Indian', 'Pakistani', 'Bangladeshi', 'Nigerian', 'Ghanaian',
      'Kenyan', 'Other'
    ];
  }

  static List<String> _getEeaCountriesList() {
    return [
      'Austria', 'Belgium', 'Bulgaria', 'Croatia', 'Cyprus', 'Czech Republic',
      'Denmark', 'Estonia', 'Finland', 'France', 'Germany', 'Greece',
      'Hungary', 'Iceland', 'Ireland', 'Italy', 'Latvia', 'Liechtenstein',
      'Lithuania', 'Luxembourg', 'Malta', 'Netherlands', 'Norway',
      'Poland', 'Portugal', 'Romania', 'Slovakia', 'Slovenia', 'Spain',
      'Sweden'
    ];
  }
}
