import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';

class DocumentConfigurationService {
  static Future<List<DocumentType>> loadAllDocuments() async {
    // In production, this would make an API call to load from backend
    // For now, return the comprehensive document configurations
    return _getComprehensiveDocumentTypes();
  }

  static Future<List<DocumentType>> loadDocumentsForApplicant(ApplicantDetailsData applicantData) async {
    final allDocuments = await loadAllDocuments();
    
    // Apply smart filtering based on applicant data
    return allDocuments.where((document) {
      return _shouldShowDocumentForApplicant(document, applicantData);
    }).toList();
  }

  static bool _shouldShowDocumentForApplicant(DocumentType document, ApplicantDetailsData applicantData) {
    final smartFiltering = document.smartFiltering ?? {};
    
    // Check nationality-based filtering
    final nationality = applicantData.additionalApplicantDetails?.birthNationality?.toLowerCase() ?? '';
    final hideForNationality = smartFiltering['hide_for_nationality'] as List<String>? ?? [];
    final showForNationality = smartFiltering['show_for_nationality'] as List<String>? ?? ['ANY'];
    
    if (hideForNationality.contains(nationality) || hideForNationality.contains('non-UK') && nationality != 'british') {
      return false;
    }
    
    if (!showForNationality.contains('ANY') && !showForNationality.contains(nationality)) {
      return false;
    }
    
    // Check age-based filtering
    final ageRestrictions = smartFiltering['age_restrictions'] as List<int>?;
    if (ageRestrictions != null && ageRestrictions.length == 2) {
      final age = _calculateAge(DateTime.parse(applicantData.dateOfBirth));
      if (age < ageRestrictions[0] || age > ageRestrictions[1]) {
        return false;
      }
    }
    
    // Check address-based filtering
    final addressCountry = applicantData.currentAddress?.countryCode ?? 'GB';
    final hideForAddress = smartFiltering['hide_for_address'] as List<String>? ?? [];
    final showForAddress = smartFiltering['show_for_address'] as List<String>? ?? ['ANY'];
    
    if (hideForAddress.contains(addressCountry) || hideForAddress.contains('UK') && addressCountry == 'GB') {
      return false;
    }
    
    if (!showForAddress.contains('ANY') && !showForAddress.contains(addressCountry)) {
      return false;
    }
    
    return true;
  }

  static int _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  static List<DocumentType> _getComprehensiveDocumentTypes() {
    return [
      // GROUP 1: Primary Identity Documents
      DocumentType(
        key: 'passport_any',
        name: 'Passport (Any)',
        documentGroup: '1',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['ANY'],
        notes: 'UK passports may be accepted up to 6 months past expiry (grace period)',
        dataFields: [
          DocumentDataField(
            name: 'passport_number',
            type: 'string',
            required: true,
            label: 'Passport Number',
            placeholder: 'Enter passport number',
            validationRules: {
              'required': true,
              'format_check': true
            },
          ),
          DocumentDataField(
            name: 'country_of_issue',
            type: 'dropdown',
            required: true,
            label: 'Country of Issue',
            placeholder: 'Select country',
            options: 'countries_list',
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'nationality',
            type: 'dropdown',
            required: true,
            label: 'Nationality',
            placeholder: 'Select nationality',
            options: 'nationalities_list',
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'date_of_issue',
            type: 'date',
            required: true,
            label: 'Date of Issue',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'not_future': true
            },
          ),
          DocumentDataField(
            name: 'date_of_expiry',
            type: 'date',
            required: true,
            label: 'Date of Expiry',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'uk_grace_period': 180 // 6 months in days
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'e_visa_uk',
        name: 'e-Visa (UK)',
        documentGroup: '1',
        requiresPhoto: false,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'Hide this for UK Nationality',
        dataFields: [
          DocumentDataField(
            name: 'ukvi_share_code',
            type: 'string',
            required: true,
            label: 'UKVI Share Code',
            placeholder: 'Enter your general share code',
            helpText: 'Provide your general share code for UK immigration status',
            validationRules: {
              'required': true,
              'home_office_verification': true
            },
          ),
          DocumentDataField(
            name: 'date_of_issue',
            type: 'date',
            required: true,
            label: 'Date of Issue',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'not_future': true
            },
          ),
          DocumentDataField(
            name: 'date_of_expiry',
            type: 'date',
            required: true,
            label: 'Date of Expiry',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'must_be_valid': true
            },
          ),
          DocumentDataField(
            name: 'visa_reference_number',
            type: 'string',
            required: false,
            label: 'Visa/Reference Number',
            placeholder: 'Enter visa or permit number (if applicable)',
            validationRules: {
              'home_office_match': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': ['British', 'UK'],
          'show_for_nationality': ['non-UK'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'biometric_residence_permit_uk',
        name: 'Biometric Residence Permit (UK)',
        documentGroup: '1',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'Hide this for UK Nationality. BRP with indefinite leave accepted up to 18 months past expiry',
        dataFields: [
          DocumentDataField(
            name: 'brp_number',
            type: 'string',
            required: true,
            label: 'BRP Number',
            placeholder: 'Enter BRP card number (typically 12 digits)',
            validationRules: {
              'required': true,
              'format': '12_digits'
            },
          ),
          DocumentDataField(
            name: 'immigration_status',
            type: 'dropdown',
            required: true,
            label: 'Immigration Status/Type',
            placeholder: 'Select status',
            options: [
              'Indefinite Leave to Remain',
              'Indefinite Leave to Enter',
              'No Time Limit',
              'Other limited leave'
            ],
            validationRules: {
              'required': true
            },
          ),
          DocumentDataField(
            name: 'date_of_expiry',
            type: 'date',
            required: true,
            label: 'Date of Expiry',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'brp_expiry_rules': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': ['British', 'UK'],
          'show_for_nationality': ['non-UK'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'application_registration_card_uk',
        name: 'Application Registration Card (ARC, UK)',
        documentGroup: '1',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'Hide this for UK Nationality. Must have valid share code or positive verification note',
        dataFields: [
          DocumentDataField(
            name: 'arc_number',
            type: 'string',
            required: true,
            label: 'ARC Number',
            placeholder: 'Enter ARC number (usually starts with "ARC")',
            validationRules: {
              'required': true,
              'arc_format': true
            },
          ),
          DocumentDataField(
            name: 'date_of_issue',
            type: 'date',
            required: true,
            label: 'Date of Issue',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'not_future': true
            },
          ),
          DocumentDataField(
            name: 'has_valid_share_code',
            type: 'yes_no',
            required: true,
            label: 'Do you have Valid Share code or Positive Verification Note?',
            validationRules: {
              'required': true,
              'must_be_yes': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': ['British', 'UK'],
          'show_for_nationality': ['non-UK'],
          'age_restrictions': null
        },
      ),

      DocumentType(
        key: 'driving_licence_uk_photocard',
        name: 'Photocard Driving Licence (UK)',
        documentGroup: '1',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['GB', 'UK'],
        notes: 'UK licence numbers encode name and DOB for verification',
        dataFields: [
          DocumentDataField(
            name: 'licence_number',
            type: 'string',
            required: true,
            label: 'Licence Number',
            placeholder: 'Enter full driving licence number (16 characters)',
            helpText: 'UK driving licence numbers encode your surname, date of birth, and gender. We will verify this matches your application details.',
            validationRules: {
              'required': true,
              'uk_format': '16_characters',
              'encode_name_dob': true,
              'cross_check_applicant': true
            },
          ),
          DocumentDataField(
            name: 'date_of_issue',
            type: 'date',
            required: true,
            label: 'Date of Issue',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'not_future': true
            },
          ),
          DocumentDataField(
            name: 'date_of_expiry',
            type: 'date',
            required: true,
            label: 'Date of Expiry',
            placeholder: 'Select date',
            validationRules: {
              'required': true,
              'must_be_valid': true
            },
          ),
        ],
        smartFiltering: {
          'hide_for_nationality': [],
          'show_for_nationality': ['ANY'],
          'age_restrictions': null
        },
      ),
    ];
  }
}
