import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_applicant_context_service.dart';

class DocumentAvailability {
  final bool isAvailable;
  final List<String> reasons;
  final String? infoMessage;
  final DocumentRecommendationLevel recommendationLevel;

  const DocumentAvailability({
    required this.isAvailable,
    required this.reasons,
    this.infoMessage,
    required this.recommendationLevel,
  });
}

enum DocumentRecommendationLevel {
  highly_recommended,
  recommended,
  available,
  not_recommended,
  unavailable,
}

class SmartDocumentFilteringService {
  static List<DocumentType> filterAvailableDocuments(
    List<DocumentType> allDocuments,
    ApplicantContext context,
  ) {
    return allDocuments.where((document) {
      return isDocumentAvailable(document.key, context);
    }).map((document) {
      return document.copyWith(
        name: SmartApplicantContextService.getSmartDocumentName(document.name, document.key),
      );
    }).toList();
  }

  static bool isDocumentAvailable(String documentKey, ApplicantContext context) {
    switch (documentKey) {
      // Age-restricted documents
      case 'letter_from_head_teacher_college_principal':
        return context.isYoungApplicant;

      // Nationality/residence-specific documents
      case 'irish_passport_card':
        return context.hasIrishAddresses || context.nationality.contains('irish');

      case 'eea_national_id_card':
        return context.isEuNational || context.hasEuAddresses;

      case 'driving_licence_photocard_non_uk':
        return !context.isUkNational || context.hasEuAddresses;

      case 'bank_building_society_statement_non_uk':
        return context.hasEuAddresses || !context.primaryResidenceCountry.contains('uk');

      case 'immigration_document_visa_work_permit_non_uk':
        return !context.isUkNational;

      case 'letter_of_sponsorship_future_uk_employer':
        return !context.hasUkAddresses && !context.isUkNational;

      // UK-specific documents
      case 'driving_licence_uk_photocard':
      case 'driving_licence_paper_uk_pre_2000':
      case 'birth_certificate_12_months':
      case 'birth_certificate_after_birth':
      case 'adoption_certificate_uk_ci':
        return context.hasUkAddresses || context.isUkNational;

      // Generally available documents
      default:
        return true;
    }
  }

  static DocumentAvailability getDocumentAvailability(
    String documentKey,
    ApplicantContext context,
  ) {
    final isAvailable = isDocumentAvailable(documentKey, context);
    final reasons = SmartApplicantContextService.getDocumentAvailabilityReasons(documentKey, context);
    final recommendationLevel = _getRecommendationLevel(documentKey, context);
    final infoMessage = _getInfoMessage(documentKey, context);

    return DocumentAvailability(
      isAvailable: isAvailable,
      reasons: reasons,
      infoMessage: infoMessage,
      recommendationLevel: recommendationLevel,
    );
  }

  static DocumentRecommendationLevel _getRecommendationLevel(
    String documentKey,
    ApplicantContext context,
  ) {
    if (!isDocumentAvailable(documentKey, context)) {
      return DocumentRecommendationLevel.unavailable;
    }

    // Highly recommended documents
    if (context.isUkNational) {
      switch (documentKey) {
        case 'passport_current':
        case 'driving_licence_uk_photocard':
          return DocumentRecommendationLevel.highly_recommended;
        case 'utility_bill_uk_not_mobile':
        case 'bank_building_society_statement_uk_ci':
          return DocumentRecommendationLevel.recommended;
      }
    }

    // For non-UK nationals
    if (!context.isUkNational) {
      switch (documentKey) {
        case 'passport_current':
        case 'biometric_residence_permit_uk':
        case 'e_visa':
          return DocumentRecommendationLevel.highly_recommended;
      }
    }

    // Young applicants
    if (context.isYoungApplicant) {
      switch (documentKey) {
        case 'letter_from_head_teacher_college_principal':
          return DocumentRecommendationLevel.recommended;
      }
    }

    // Default to available
    return DocumentRecommendationLevel.available;
  }

  static String? _getInfoMessage(String documentKey, ApplicantContext context) {
    switch (documentKey) {
      case 'passport_current':
        if (context.isUkNational) {
          return 'UK passports can be accepted up to 6 months past expiry';
        }
        return 'Must be current and valid';

      case 'biometric_residence_permit_uk':
        return 'BRP with indefinite leave can be accepted up to 18 months past expiry';

      case 'driving_licence_uk_photocard':
        return 'Your licence number will be verified against your personal details';

      case 'letter_from_head_teacher_college_principal':
        if (context.isYoungApplicant) {
          return 'Only use this if no other documents are available. Must be issued within 1 month';
        }
        return 'Only available for applicants aged 16-19 in full-time education';

      case 'irish_passport_card':
        if (!context.hasIrishAddresses && !context.nationality.contains('irish')) {
          return 'Only available to Irish citizens or those with Irish residence history';
        }
        return 'Cannot be used alongside an Irish passport';

      case 'utility_bill_uk_not_mobile':
        return 'Mobile phone bills are not accepted. Must be issued within 3 months';

      case 'mortgage_statement_uk':
        return 'Must be issued within 12 months by a recognized UK lender';

      case 'bank_building_society_statement_uk_ci':
        return 'Printed online statements not accepted unless stamped by bank';

      case 'ehic_ghic_uk':
        return 'Only UK-issued EHIC/GHIC cards are accepted';

      default:
        return null;
    }
  }

  static List<DocumentType> sortDocumentsByRecommendation(
    List<DocumentType> documents,
    ApplicantContext context,
  ) {
    return documents..sort((a, b) {
      final aLevel = _getRecommendationLevel(a.key, context);
      final bLevel = _getRecommendationLevel(b.key, context);
      
      // Sort by recommendation level (highly recommended first)
      final levelComparison = aLevel.index.compareTo(bLevel.index);
      if (levelComparison != 0) return levelComparison;
      
      // Then sort alphabetically
      return a.name.compareTo(b.name);
    });
  }

  static Map<String, List<DocumentType>> groupDocumentsByRecommendation(
    List<DocumentType> documents,
    ApplicantContext context,
  ) {
    final groups = <String, List<DocumentType>>{
      'Highly Recommended': [],
      'Recommended': [],
      'Available': [],
      'Not Recommended': [],
    };

    for (final document in documents) {
      final level = _getRecommendationLevel(document.key, context);
      
      switch (level) {
        case DocumentRecommendationLevel.highly_recommended:
          groups['Highly Recommended']!.add(document);
          break;
        case DocumentRecommendationLevel.recommended:
          groups['Recommended']!.add(document);
          break;
        case DocumentRecommendationLevel.available:
          groups['Available']!.add(document);
          break;
        case DocumentRecommendationLevel.not_recommended:
          groups['Not Recommended']!.add(document);
          break;
        case DocumentRecommendationLevel.unavailable:
          // Don't include unavailable documents
          break;
      }
    }

    // Remove empty groups
    groups.removeWhere((key, value) => value.isEmpty);
    
    return groups;
  }

  static List<String> getPersonalizedGuidance(ApplicantContext context) {
    final guidance = <String>[];

    if (context.isUkNational) {
      guidance.addAll([
        'As a UK national, your passport or driving licence provides the strongest identity verification',
        'You\'ll need one additional document to complete Route 1 (the easiest route)',
        'Recent utility bills or bank statements work well for address confirmation',
      ]);
    } else {
      guidance.addAll([
        'Your passport is essential for identity verification',
        'If you have a BRP or e-Visa, these are highly valuable documents',
        'You may need additional documents to complete your chosen route',
      ]);
    }

    if (context.isYoungApplicant) {
      guidance.add('As a young applicant, a letter from your school/college can be used if other documents aren\'t available');
    }

    if (context.isRecentImmigrant) {
      guidance.add('Your immigration documents (BRP, e-Visa) are particularly important for establishing your status');
    }

    return guidance;
  }

  static String getRouteRecommendation(ApplicantContext context) {
    if (context.isUkNational) {
      return 'Route 1 is recommended: Use your UK passport or driving licence plus one additional document from any group';
    } else if (context.hasUkAddresses) {
      return 'Route 1 is recommended: Use your passport plus one additional document from any group';
    } else {
      return 'Route 2 may be easier: Use one government document plus two additional documents';
    }
  }
}
