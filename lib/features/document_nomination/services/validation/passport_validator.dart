import 'package:SolidCheck/features/document_nomination/services/validation/validation_result.dart';

class PassportValidator {
  static ValidationResult validatePassportNumber({
    required String passportNumber,
    required String countryOfIssue,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (passportNumber.isEmpty) {
      errors.add('Passport number is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    final cleanPassportNumber = passportNumber.replaceAll(' ', '').toUpperCase();

    if (countryOfIssue.toUpperCase() == 'GB' || countryOfIssue.toUpperCase() == 'UK' || countryOfIssue.toLowerCase() == 'united kingdom') {
      if (cleanPassportNumber.length != 9) {
        errors.add('UK passport number must be exactly 9 characters');
      } else if (!RegExp(r'^[0-9]{9}$').hasMatch(cleanPassportNumber)) {
        errors.add('UK passport number must contain only digits');
      }
    } else {
      if (cleanPassportNumber.length < 6 || cleanPassportNumber.length > 12) {
        warnings.add('Foreign passport numbers typically range from 6-12 characters');
      }
      
      if (!RegExp(r'^[A-Z0-9]+$').hasMatch(cleanPassportNumber)) {
        warnings.add('Passport number contains unusual characters - verify authenticity');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validatePassportDates({
    required DateTime? issueDate,
    required DateTime? expiryDate,
    required String countryOfIssue,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (issueDate == null) {
      errors.add('Issue date is required');
    }

    if (expiryDate == null) {
      errors.add('Expiry date is required');
    }

    if (issueDate != null && expiryDate != null) {
      if (issueDate.isAfter(expiryDate)) {
        errors.add('Issue date cannot be after expiry date');
      }

      final now = DateTime.now();
      
      if (issueDate.isAfter(now)) {
        errors.add('Issue date cannot be in the future');
      }

      final isUkPassport = countryOfIssue.toUpperCase() == 'GB' || 
                          countryOfIssue.toUpperCase() == 'UK' || 
                          countryOfIssue.toLowerCase() == 'united kingdom';

      if (expiryDate.isBefore(now)) {
        if (isUkPassport) {
          final daysPastExpiry = now.difference(expiryDate).inDays;
          if (daysPastExpiry <= 180) {
            warnings.add('UK passport is expired but within 6-month grace period');
          } else {
            errors.add('UK passport is expired beyond the 6-month grace period');
          }
        } else {
          errors.add('Passport has expired and must be current and valid');
        }
      }

      final validityPeriod = expiryDate.difference(issueDate).inDays;
      if (isUkPassport) {
        if (validityPeriod < 3650 || validityPeriod > 3660) {
          warnings.add('UK passport validity period should be approximately 10 years');
        }
      } else {
        if (validityPeriod < 1800 || validityPeriod > 3660) {
          warnings.add('Passport validity period appears unusual - verify authenticity');
        }
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateCountryAndNationality({
    required String countryOfIssue,
    required String nationality,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (countryOfIssue.isEmpty) {
      errors.add('Country of issue is required');
    }

    if (nationality.isEmpty) {
      errors.add('Nationality is required');
    }

    if (countryOfIssue.isNotEmpty && nationality.isNotEmpty) {
      final normalizedCountry = _normalizeCountryName(countryOfIssue);
      final normalizedNationality = _normalizeNationalityName(nationality);

      final countryNationalityMap = {
        'united kingdom': ['british', 'uk', 'united kingdom'],
        'uk': ['british', 'uk', 'united kingdom'],
        'gb': ['british', 'uk', 'united kingdom'],
        'ireland': ['irish', 'ireland'],
        'france': ['french', 'france'],
        'germany': ['german', 'germany'],
        'spain': ['spanish', 'spain'],
        'italy': ['italian', 'italy'],
        'netherlands': ['dutch', 'netherlands'],
        'belgium': ['belgian', 'belgium'],
        'portugal': ['portuguese', 'portugal'],
        'poland': ['polish', 'poland'],
      };

      final expectedNationalities = countryNationalityMap[normalizedCountry];
      if (expectedNationalities != null) {
        final nationalityMatches = expectedNationalities.any((expected) => 
          normalizedNationality.contains(expected) || expected.contains(normalizedNationality));
        
        if (!nationalityMatches) {
          warnings.add('Country of issue and nationality do not appear to match - verify passport details');
        }
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateEPassportChip({
    required bool? hasEPassportChip,
    required DateTime? issueDate,
    required String countryOfIssue,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (hasEPassportChip == null) {
      errors.add('E-passport chip status is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    if (issueDate != null) {
      final isUkPassport = countryOfIssue.toUpperCase() == 'GB' || 
                          countryOfIssue.toUpperCase() == 'UK' || 
                          countryOfIssue.toLowerCase() == 'united kingdom';

      if (isUkPassport) {
        final ukEPassportStart = DateTime(2006, 3, 1);
        if (issueDate.isAfter(ukEPassportStart) && !hasEPassportChip) {
          warnings.add('UK passports issued after March 2006 should have e-passport chips');
        }
        if (issueDate.isBefore(ukEPassportStart) && hasEPassportChip) {
          warnings.add('UK passports issued before March 2006 typically do not have e-passport chips');
        }
      } else {
        final generalEPassportStart = DateTime(2005, 1, 1);
        if (issueDate.isAfter(generalEPassportStart) && !hasEPassportChip) {
          warnings.add('Most passports issued after 2005 should have e-passport chips');
        }
      }
    }

    if (hasEPassportChip) {
      warnings.add('Verify e-passport symbol is visible on passport cover');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validatePassportSecurity({
    required String passportNumber,
    required DateTime? issueDate,
    required String countryOfIssue,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    warnings.addAll([
      'Check passport for signs of tampering or alteration',
      'Verify passport pages are securely bound',
      'Check photo is properly embedded and not replaced',
      'Verify security features (watermarks, holograms) are present',
      'Ensure passport details are clearly printed and legible',
    ]);

    if (issueDate != null) {
      final age = DateTime.now().difference(issueDate).inDays;
      if (age > 3650) {
        warnings.add('Passport is over 10 years old - check for wear and security feature integrity');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static String _normalizeCountryName(String country) {
    return country.toLowerCase()
        .replaceAll('united kingdom', 'united kingdom')
        .replaceAll('great britain', 'united kingdom')
        .replaceAll('britain', 'united kingdom')
        .trim();
  }

  static String _normalizeNationalityName(String nationality) {
    return nationality.toLowerCase()
        .replaceAll('british citizen', 'british')
        .replaceAll('uk citizen', 'british')
        .trim();
  }

  static ValidationResult validatePassportComplete({
    required String passportNumber,
    required String countryOfIssue,
    required String nationality,
    required DateTime? issueDate,
    required DateTime? expiryDate,
    required bool? hasEPassportChip,
  }) {
    final results = [
      validatePassportNumber(passportNumber: passportNumber, countryOfIssue: countryOfIssue),
      validatePassportDates(issueDate: issueDate, expiryDate: expiryDate, countryOfIssue: countryOfIssue),
      validateCountryAndNationality(countryOfIssue: countryOfIssue, nationality: nationality),
      validateEPassportChip(hasEPassportChip: hasEPassportChip, issueDate: issueDate, countryOfIssue: countryOfIssue),
      validatePassportSecurity(passportNumber: passportNumber, issueDate: issueDate, countryOfIssue: countryOfIssue),
    ];

    return results.reduce((combined, result) => combined.merge(result));
  }
}
