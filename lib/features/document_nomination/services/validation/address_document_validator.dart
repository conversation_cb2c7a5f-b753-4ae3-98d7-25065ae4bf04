import 'package:SolidCheck/features/document_nomination/services/validation/validation_result.dart';

class AddressDocumentValidator {
  static ValidationResult validateMortgageStatement({
    required String lenderName,
    required String accountNumber,
    required DateTime? issueDate,
    required String? propertyAddress,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (lenderName.isEmpty) {
      errors.add('Lender name is required');
    } else {
      final recognizedLenders = ['halifax', 'nationwide', 'santander', 'barclays', 'lloyds', 'hsbc', 'natwest', 'rbs', 'tesco', 'virgin', 'first direct', 'yorkshire', 'coventry', 'skipton'];
      final isRecognized = recognizedLenders.any((lender) => 
        lenderName.toLowerCase().contains(lender));
      
      if (!isRecognized) {
        warnings.add('Verify lender is a recognized UK mortgage provider');
      }
    }

    if (accountNumber.isEmpty) {
      errors.add('Account/reference number is required');
    }

    final validationResult = _validateDocumentAge(issueDate, 365, 'Mortgage statement');
    errors.addAll(validationResult.errors);
    warnings.addAll(validationResult.warnings);

    if (propertyAddress?.isEmpty ?? true) {
      warnings.add('Property address helps verify current address');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateBankStatement({
    required String bankName,
    required String accountNumber,
    required DateTime? statementDate,
    required String? addressOnStatement,
    required bool isUkStatement,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (bankName.isEmpty) {
      errors.add('Bank name is required');
    }

    if (accountNumber.isEmpty) {
      errors.add('Account number is required');
    } else if (isUkStatement && accountNumber.length < 4) {
      warnings.add('UK bank statements should show at least last 4 digits of account number');
    }

    final validityDays = isUkStatement ? 90 : 90;
    final validationResult = _validateDocumentAge(statementDate, validityDays, 'Bank statement');
    errors.addAll(validationResult.errors);
    warnings.addAll(validationResult.warnings);

    if (isUkStatement) {
      warnings.add('Printouts or online-only statements not allowed unless stamped by bank');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateUtilityBill({
    required String providerName,
    required String accountHolderName,
    required String serviceAddress,
    required DateTime? issueDate,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (providerName.isEmpty) {
      errors.add('Provider name is required');
    } else {
      final mobileProviders = ['ee', 'o2', 'vodafone', 'three', '3', 'giffgaff', 'tesco mobile', 'virgin mobile'];
      final isMobileProvider = mobileProviders.any((provider) => 
        providerName.toLowerCase().contains(provider));
      
      if (isMobileProvider) {
        errors.add('Mobile phone bills are not accepted as utility bills');
      }

      final utilityProviders = ['british gas', 'eon', 'edf', 'scottish power', 'npower', 'sse', 'bulb', 'octopus', 'thames water', 'anglian water', 'severn trent', 'united utilities'];
      final isUtilityProvider = utilityProviders.any((provider) => 
        providerName.toLowerCase().contains(provider));
      
      if (!isUtilityProvider && !isMobileProvider) {
        warnings.add('Verify provider is a recognized UK utility company');
      }
    }

    if (accountHolderName.isEmpty) {
      errors.add('Account holder name is required');
    }

    if (serviceAddress.isEmpty) {
      errors.add('Service address is required');
    }

    final validationResult = _validateDocumentAge(issueDate, 90, 'Utility bill');
    errors.addAll(validationResult.errors);
    warnings.addAll(validationResult.warnings);

    warnings.add('Must not be a printed online copy');

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateCouncilTaxStatement({
    required String councilName,
    required String taxYear,
    required DateTime? issueDate,
    required String propertyAddress,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (councilName.isEmpty) {
      errors.add('Council name is required');
    } else if (!councilName.toLowerCase().contains('council') && 
               !councilName.toLowerCase().contains('borough') &&
               !councilName.toLowerCase().contains('city')) {
      warnings.add('Verify issuing authority is a UK or CI council');
    }

    if (taxYear.isEmpty) {
      errors.add('Tax year is required');
    } else {
      final currentYear = DateTime.now().year;
      final yearPattern = RegExp(r'(\d{4})');
      final matches = yearPattern.allMatches(taxYear);
      
      if (matches.isNotEmpty) {
        final year = int.parse(matches.first.group(1)!);
        if (year < currentYear - 2 || year > currentYear + 1) {
          warnings.add('Tax year should be current or most recent year');
        }
      }
    }

    if (propertyAddress.isEmpty) {
      errors.add('Property address is required');
    }

    final validationResult = _validateDocumentAge(issueDate, 365, 'Council tax statement');
    errors.addAll(validationResult.errors);
    warnings.addAll(validationResult.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateP45P60({
    required String employerName,
    required String documentTypeAndYear,
    required DateTime? issueDate,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (employerName.isEmpty) {
      errors.add('Employer name is required');
    }

    if (documentTypeAndYear.isEmpty) {
      errors.add('Document type and year is required');
    } else {
      final isP45 = documentTypeAndYear.toLowerCase().contains('p45');
      final isP60 = documentTypeAndYear.toLowerCase().contains('p60');
      
      if (!isP45 && !isP60) {
        errors.add('Document must be either P45 or P60');
      }

      final yearPattern = RegExp(r'(\d{4})');
      final matches = yearPattern.allMatches(documentTypeAndYear);
      
      if (matches.isNotEmpty) {
        final year = int.parse(matches.first.group(1)!);
        final currentYear = DateTime.now().year;
        
        if (year < currentYear - 2 || year > currentYear) {
          warnings.add('Tax year should be recent (within last 2 years)');
        }
      }
    }

    final validationResult = _validateDocumentAge(issueDate, 365, 'P45/P60');
    errors.addAll(validationResult.errors);
    warnings.addAll(validationResult.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateBenefitStatement({
    required String benefitType,
    required String issuingAuthority,
    required DateTime? issueDate,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (benefitType.isEmpty) {
      errors.add('Benefit type is required');
    }

    if (issuingAuthority.isEmpty) {
      errors.add('Issuing authority is required');
    } else {
      final validAuthorities = ['dwp', 'hmrc', 'department for work and pensions', 'hm revenue and customs', 'local authority', 'council'];
      final isValidAuthority = validAuthorities.any((authority) => 
        issuingAuthority.toLowerCase().contains(authority));
      
      if (!isValidAuthority) {
        warnings.add('Verify issuing authority is a UK/CI government agency');
      }
    }

    final validationResult = _validateDocumentAge(issueDate, 90, 'Benefit statement');
    errors.addAll(validationResult.errors);
    warnings.addAll(validationResult.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateEhicGhic({
    required String cardNumber,
    required DateTime? expiryDate,
    required String countryCode,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (cardNumber.isEmpty) {
      errors.add('Card number is required');
    }

    if (expiryDate == null) {
      errors.add('Expiry date is required');
    } else {
      final now = DateTime.now();
      if (expiryDate.isBefore(now)) {
        errors.add('EHIC/GHIC card has expired');
      }
    }

    if (countryCode.isEmpty) {
      errors.add('Country code is required');
    } else if (countryCode.toUpperCase() != 'UK' && countryCode.toUpperCase() != 'GB') {
      errors.add('Only UK-issued EHIC/GHIC cards are accepted');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validatePassIdCard({
    required String passNumber,
    required String issuingScheme,
    required DateTime? expiryDate,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (passNumber.isEmpty) {
      errors.add('PASS number is required');
    }

    if (issuingScheme.isEmpty) {
      errors.add('Issuing scheme is required');
    } else {
      warnings.add('Verify card bears official PASS logo');
    }

    if (expiryDate == null) {
      errors.add('Expiry date is required');
    } else {
      final now = DateTime.now();
      if (expiryDate.isBefore(now)) {
        errors.add('PASS ID card has expired');
      }
    }

    warnings.add('Digital PASS cards with QR code are acceptable if validated');

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult _validateDocumentAge(DateTime? issueDate, int maxDays, String documentType) {
    final errors = <String>[];
    final warnings = <String>[];

    if (issueDate == null) {
      errors.add('Issue date is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    final now = DateTime.now();
    
    if (issueDate.isAfter(now)) {
      errors.add('Issue date cannot be in the future');
    }

    final daysSinceIssue = now.difference(issueDate).inDays;
    
    if (daysSinceIssue > maxDays) {
      final monthsAllowed = (maxDays / 30).round();
      errors.add('$documentType must be issued within the last $monthsAllowed month${monthsAllowed == 1 ? '' : 's'}');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
}
