import 'package:SolidCheck/features/document_nomination/services/validation/validation_result.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/uk_driving_licence_validator.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/birth_certificate_validator.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/passport_validator.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/address_document_validator.dart';

class DocumentValidationService {
  static ValidationResult validateDocumentData({
    required String documentKey,
    required Map<String, dynamic> documentData,
    Map<String, dynamic>? applicantData,
  }) {
    switch (documentKey) {
      case 'passport_current':
        return _validatePassport(documentData);
      
      case 'driving_licence_uk_photocard':
        return _validateUkDrivingLicence(documentData, applicantData, isPhotocard: true);
      
      case 'driving_licence_paper_uk_pre_2000':
        return _validateUkDrivingLicence(documentData, applicantData, isPhotocard: false);
      
      case 'driving_licence_photocard_non_uk':
        return _validateNonUkDrivingLicence(documentData);
      
      case 'birth_certificate_12_months':
        return _validateGroup1BirthCertificate(documentData);
      
      case 'birth_certificate_after_birth':
        return _validateGroup2aBirthCertificate(documentData);
      
      case 'adoption_certificate_uk_ci':
        return _validateAdoptionCertificate(documentData);
      
      case 'mortgage_statement_uk':
        return _validateMortgageStatement(documentData);
      
      case 'bank_building_society_statement_uk_ci':
        return _validateBankStatement(documentData, isUk: true);
      
      case 'bank_building_society_statement_non_uk':
        return _validateBankStatement(documentData, isUk: false);
      
      case 'utility_bill_uk_not_mobile':
        return _validateUtilityBill(documentData);
      
      case 'council_tax_statement_uk_ci':
        return _validateCouncilTaxStatement(documentData);
      
      case 'p45_p60_uk_ci':
        return _validateP45P60(documentData);
      
      case 'benefit_statement_uk':
        return _validateBenefitStatement(documentData);
      
      case 'ehic_ghic_uk':
        return _validateEhicGhic(documentData);
      
      case 'pass_id_card_uk_iom_ci':
        return _validatePassIdCard(documentData);
      
      default:
        return _validateGenericDocument(documentData);
    }
  }

  static ValidationResult _validatePassport(Map<String, dynamic> data) {
    return PassportValidator.validatePassportComplete(
      passportNumber: data['passport_number']?.toString() ?? '',
      countryOfIssue: data['country_of_issue']?.toString() ?? '',
      nationality: data['nationality']?.toString() ?? '',
      issueDate: _parseDate(data['date_of_issue']),
      expiryDate: _parseDate(data['date_of_expiry']),
      hasEPassportChip: data['e_passport_chip'] as bool?,
    );
  }

  static ValidationResult _validateUkDrivingLicence(
    Map<String, dynamic> data,
    Map<String, dynamic>? applicantData, {
    required bool isPhotocard,
  }) {
    final licenceNumber = data['licence_number']?.toString() ?? '';
    final issueDate = _parseDate(data['date_of_issue']);
    final expiryDate = _parseDate(data['date_of_expiry']);

    ValidationResult result;

    if (isPhotocard) {
      result = UkDrivingLicenceValidator.validatePhotoLicence(
        issueDate: issueDate,
        expiryDate: expiryDate,
      );
    } else {
      result = UkDrivingLicenceValidator.validatePaperLicence(
        issueDate: issueDate,
        expiryDate: expiryDate,
        dateOfBirth: applicantData != null ? _parseDate(applicantData['date_of_birth']) : null,
      );
    }

    if (applicantData != null && licenceNumber.isNotEmpty) {
      final licenceValidation = UkDrivingLicenceValidator.validateLicenceNumber(
        licenceNumber: licenceNumber,
        surname: applicantData['surname']?.toString() ?? '',
        firstName: applicantData['first_name']?.toString() ?? '',
        middleName: applicantData['middle_name']?.toString(),
        dateOfBirth: _parseDate(applicantData['date_of_birth']) ?? DateTime.now(),
        gender: applicantData['gender']?.toString() ?? '',
      );
      
      result = result.merge(licenceValidation);
    }

    return result;
  }

  static ValidationResult _validateNonUkDrivingLicence(Map<String, dynamic> data) {
    final errors = <String>[];
    final warnings = <String>[];

    final licenceNumber = data['licence_number']?.toString() ?? '';
    final issueDate = _parseDate(data['issue_date']);
    final expiryDate = _parseDate(data['expiry_date']);
    final countryOfIssue = data['country_of_issue']?.toString() ?? '';

    if (licenceNumber.isEmpty) {
      errors.add('Licence number is required');
    }

    if (issueDate == null) {
      errors.add('Issue date is required');
    }

    if (expiryDate == null) {
      errors.add('Expiry date is required');
    } else {
      final now = DateTime.now();
      if (expiryDate.isBefore(now)) {
        errors.add('Driving licence has expired');
      }
    }

    if (countryOfIssue.isEmpty) {
      errors.add('Country of issue is required');
    } else {
      final ukCountries = ['uk', 'united kingdom', 'gb', 'great britain', 'england', 'wales', 'scotland', 'northern ireland'];
      final isUkCountry = ukCountries.any((country) => 
        countryOfIssue.toLowerCase().contains(country));
      
      if (isUkCountry) {
        errors.add('UK licences should use the UK driving licence document type');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult _validateGroup1BirthCertificate(Map<String, dynamic> data) {
    return BirthCertificateValidator.validateGroup1BirthCertificate(
      dateOfBirth: _parseDate(data['date_of_birth']),
      registrationDate: _parseDate(data['date_of_issue_registration_date']),
      placeOfBirth: data['place_and_country_of_birth']?.toString() ?? '',
      certificateNumber: data['certificate_reference_number']?.toString(),
    );
  }

  static ValidationResult _validateGroup2aBirthCertificate(Map<String, dynamic> data) {
    return BirthCertificateValidator.validateGroup2aBirthCertificate(
      dateOfBirth: _parseDate(data['date_of_birth']),
      registrationDate: _parseDate(data['issue_date']),
      placeOfBirth: data['place_and_country_of_birth']?.toString() ?? '',
      certificateNumber: data['certificate_number']?.toString(),
    );
  }

  static ValidationResult _validateAdoptionCertificate(Map<String, dynamic> data) {
    return BirthCertificateValidator.validateAdoptionCertificate(
      adoptionDate: _parseDate(data['date_of_adoption']),
      issuingAuthority: data['issuing_authority']?.toString() ?? '',
      certificateNumber: data['certificate_number']?.toString(),
      adoptedName: data['adopted_name']?.toString(),
    );
  }

  static ValidationResult _validateMortgageStatement(Map<String, dynamic> data) {
    return AddressDocumentValidator.validateMortgageStatement(
      lenderName: data['lender_name']?.toString() ?? '',
      accountNumber: data['account_reference_number']?.toString() ?? '',
      issueDate: _parseDate(data['issue_date']),
      propertyAddress: data['property_address']?.toString(),
    );
  }

  static ValidationResult _validateBankStatement(Map<String, dynamic> data, {required bool isUk}) {
    return AddressDocumentValidator.validateBankStatement(
      bankName: data['bank_name']?.toString() ?? '',
      accountNumber: isUk 
        ? (data['account_number_last_4_digits']?.toString() ?? '')
        : (data['account_number']?.toString() ?? ''),
      statementDate: _parseDate(data['statement_date']),
      addressOnStatement: data['address_on_statement']?.toString(),
      isUkStatement: isUk,
    );
  }

  static ValidationResult _validateUtilityBill(Map<String, dynamic> data) {
    return AddressDocumentValidator.validateUtilityBill(
      providerName: data['provider_name']?.toString() ?? '',
      accountHolderName: data['account_holder_name']?.toString() ?? '',
      serviceAddress: data['service_address']?.toString() ?? '',
      issueDate: _parseDate(data['issue_date']),
    );
  }

  static ValidationResult _validateCouncilTaxStatement(Map<String, dynamic> data) {
    return AddressDocumentValidator.validateCouncilTaxStatement(
      councilName: data['council_name']?.toString() ?? '',
      taxYear: data['tax_year']?.toString() ?? '',
      issueDate: _parseDate(data['issue_date']),
      propertyAddress: data['property_address']?.toString() ?? '',
    );
  }

  static ValidationResult _validateP45P60(Map<String, dynamic> data) {
    return AddressDocumentValidator.validateP45P60(
      employerName: data['employer_name']?.toString() ?? '',
      documentTypeAndYear: data['document_type_and_year']?.toString() ?? '',
      issueDate: _parseDate(data['issue_date']),
    );
  }

  static ValidationResult _validateBenefitStatement(Map<String, dynamic> data) {
    return AddressDocumentValidator.validateBenefitStatement(
      benefitType: data['benefit_type']?.toString() ?? '',
      issuingAuthority: data['issuing_authority']?.toString() ?? '',
      issueDate: _parseDate(data['issue_date']),
    );
  }

  static ValidationResult _validateEhicGhic(Map<String, dynamic> data) {
    return AddressDocumentValidator.validateEhicGhic(
      cardNumber: data['card_number']?.toString() ?? '',
      expiryDate: _parseDate(data['expiry_date']),
      countryCode: data['country_code']?.toString() ?? '',
    );
  }

  static ValidationResult _validatePassIdCard(Map<String, dynamic> data) {
    return AddressDocumentValidator.validatePassIdCard(
      passNumber: data['pass_number']?.toString() ?? '',
      issuingScheme: data['issuing_scheme']?.toString() ?? '',
      expiryDate: _parseDate(data['expiry_date']),
    );
  }

  static ValidationResult _validateGenericDocument(Map<String, dynamic> data) {
    final errors = <String>[];
    final warnings = <String>[];

    if (data.isEmpty) {
      errors.add('Document data is required');
    }

    warnings.add('Document validation not implemented for this document type');

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static DateTime? _parseDate(dynamic dateValue) {
    if (dateValue == null) return null;
    
    if (dateValue is DateTime) return dateValue;
    
    if (dateValue is String) {
      try {
        return DateTime.parse(dateValue);
      } catch (e) {
        return null;
      }
    }
    
    return null;
  }
}
