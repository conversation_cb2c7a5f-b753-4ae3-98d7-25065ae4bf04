import 'package:SolidCheck/features/document_nomination/services/validation/validation_result.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';

class ComprehensiveDocumentValidator {
  static ValidationResult validateDocumentField({
    required String documentKey,
    required String fieldName,
    required dynamic fieldValue,
    required Map<String, dynamic> validationRules,
    Map<String, dynamic>? applicantData,
    Map<String, dynamic>? allFormData,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    // Basic required field validation
    if (validationRules['required'] == true && (fieldValue == null || fieldValue.toString().isEmpty)) {
      errors.add('${_getFieldDisplayName(fieldName)} is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    // Skip further validation if field is empty and not required
    if (fieldValue == null || fieldValue.toString().isEmpty) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    // Date validations
    if (validationRules.containsKey('not_future') && validationRules['not_future'] == true) {
      final result = _validateNotFuture(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('within_12_months') && validationRules['within_12_months'] == true) {
      final result = _validateWithinTimeframe(fieldValue, fieldName, 365);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('within_3_months') && validationRules['within_3_months'] == true) {
      final result = _validateWithinTimeframe(fieldValue, fieldName, 90);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('within_1_month') && validationRules['within_1_month'] == true) {
      final result = _validateWithinTimeframe(fieldValue, fieldName, 30);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('must_be_valid') && validationRules['must_be_valid'] == true) {
      final result = _validateNotExpired(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('uk_grace_period')) {
      final graceDaysValue = validationRules['uk_grace_period'];
      final graceDays = graceDaysValue is int ? graceDaysValue : (graceDaysValue is String ? int.tryParse(graceDaysValue) ?? 0 : 0);
      final result = _validateUkGracePeriod(fieldValue, fieldName, graceDays, allFormData);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    // Format validations
    if (validationRules.containsKey('format')) {
      final format = validationRules['format'] as String;
      final result = _validateFormat(fieldValue, fieldName, format);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('uk_format') && validationRules['uk_format'] == '16_characters') {
      final result = _validateUkLicenceFormat(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('exactly_4_digits') && validationRules['exactly_4_digits'] == true) {
      final result = _validateExactly4Digits(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    // Cross-reference validations
    if (validationRules.containsKey('match_applicant_dob') && applicantData != null) {
      final result = _validateMatchApplicantDob(fieldValue, fieldName, applicantData);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('match_applicant_address') && applicantData != null) {
      final result = _validateMatchApplicantAddress(fieldValue, fieldName, applicantData);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('match_applicant_addresses') && applicantData != null) {
      final result = _validateMatchApplicantAddresses(fieldValue, fieldName, applicantData);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    // Conditional validations
    if (validationRules.containsKey('must_be_yes') && validationRules['must_be_yes'] == true) {
      final result = _validateMustBeYes(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('before_march_2000') && validationRules['before_march_2000'] == true) {
      final result = _validateBeforeMarch2000(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('within_12_months_of_birth') && allFormData != null) {
      final result = _validateWithin12MonthsOfBirth(fieldValue, fieldName, allFormData);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    // Country/nationality validations
    if (validationRules.containsKey('must_be_uk_iom_ci') && validationRules['must_be_uk_iom_ci'] == true) {
      final result = _validateUkIomCi(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('must_be_outside_uk') && validationRules['must_be_outside_uk'] == true) {
      final result = _validateOutsideUk(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('must_be_eea_country') && validationRules['must_be_eea_country'] == true) {
      final result = _validateEeaCountry(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    // Special validations
    if (validationRules.containsKey('uk_utility_not_mobile') && validationRules['uk_utility_not_mobile'] == true) {
      final result = _validateUkUtilityNotMobile(fieldValue, fieldName);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    if (validationRules.containsKey('encode_name_dob') && validationRules['encode_name_dob'] == true && applicantData != null) {
      final result = _validateUkLicenceEncoding(fieldValue, fieldName, applicantData);
      errors.addAll(result.errors);
      warnings.addAll(result.warnings);
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static String _getFieldDisplayName(String fieldName) {
    final displayNames = {
      'passport_number': 'Passport Number',
      'country_of_issue': 'Country of Issue',
      'nationality': 'Nationality',
      'date_of_issue': 'Date of Issue',
      'date_of_expiry': 'Date of Expiry',
      'ukvi_share_code': 'UKVI Share Code',
      'visa_reference_number': 'Visa/Reference Number',
      'brp_number': 'BRP Number',
      'immigration_status': 'Immigration Status/Type',
      'arc_number': 'ARC Number',
      'has_valid_share_code': 'Valid Share Code/Verification Note',
      'licence_number': 'Licence Number',
      'date_of_birth': 'Date of Birth',
      'date_of_issue_registration': 'Date of Issue (Registration Date)',
      'issued_within_uk': 'Issued within UK',
      'date_of_adoption': 'Date of Adoption',
      'adopted_full_name': 'Adopted Full Name',
      'issue_date': 'Issue Date',
      'expiry_date': 'Expiry Date',
      'place_and_country_of_birth': 'Place and Country of Birth',
      'certificate_number': 'Certificate Number',
      'date_of_registration': 'Date of Registration',
      'issuing_authority': 'Issuing Authority',
      'document_type': 'Document Type',
      'living_working_outside_uk': 'Living and Working Outside UK',
      'document_code': 'Document Code',
      'lender_name': 'Lender Name',
      'address_postcode': 'Address Postcode',
      'bank_name': 'Bank Name',
      'account_number_last_4': 'Account Number (last 4 digits)',
      'statement_date': 'Statement Date',
      'address_postcode_on_statement': 'Address Postcode on Statement',
      'bank_country': 'Bank Country',
      'account_number': 'Account Number',
      'is_uk_bank_not_photocopy': 'UK Bank (not photocopy)',
      'employer_name': 'Employer Name',
      'document_type_and_year': 'Document Type and Year',
      'council_name': 'Council Name',
      'property_postcode': 'Property Postcode',
      'sponsor_name': 'Sponsor Name',
      'validity': 'Validity',
      'reference_number': 'Reference Number',
      'provider_name': 'Provider Name',
      'reference_type': 'Reference/Type',
      'card_number': 'Card Number',
      'is_issued_in_uk': 'Issued in UK',
      'pass_number': 'PASS Number',
      'school_college_name': 'School/College Name',
      'date_of_letter': 'Date of Letter',
      'student_name_matches': 'Student Name Matches',
    };
    
    return displayNames[fieldName] ?? fieldName.replaceAll('_', ' ').split(' ').map((word) => 
      word.isEmpty ? word : word[0].toUpperCase() + word.substring(1)).join(' ');
  }

  static ValidationResult _validateNotFuture(dynamic value, String fieldName) {
    if (value is! DateTime && value is! String) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    DateTime? date;
    if (value is String) {
      try {
        date = DateTime.parse(value);
      } catch (e) {
        return ValidationResult(isValid: false, errors: ['Invalid date format for ${_getFieldDisplayName(fieldName)}'], warnings: []);
      }
    } else {
      date = value as DateTime;
    }

    if (date.isAfter(DateTime.now())) {
      return ValidationResult(
        isValid: false, 
        errors: ['${_getFieldDisplayName(fieldName)} cannot be in the future'], 
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateWithinTimeframe(dynamic value, String fieldName, int days) {
    if (value is! DateTime && value is! String) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    DateTime? date;
    if (value is String) {
      try {
        date = DateTime.parse(value);
      } catch (e) {
        return ValidationResult(isValid: false, errors: ['Invalid date format for ${_getFieldDisplayName(fieldName)}'], warnings: []);
      }
    } else {
      date = value as DateTime;
    }

    final now = DateTime.now();
    final daysSince = now.difference(date).inDays;

    if (daysSince > days) {
      final months = (days / 30).round();
      return ValidationResult(
        isValid: false,
        errors: ['${_getFieldDisplayName(fieldName)} must be within the last $months month${months == 1 ? '' : 's'}'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateNotExpired(dynamic value, String fieldName) {
    if (value is! DateTime && value is! String) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    DateTime? date;
    if (value is String) {
      try {
        date = DateTime.parse(value);
      } catch (e) {
        return ValidationResult(isValid: false, errors: ['Invalid date format for ${_getFieldDisplayName(fieldName)}'], warnings: []);
      }
    } else {
      date = value as DateTime;
    }

    if (date.isBefore(DateTime.now())) {
      return ValidationResult(
        isValid: false,
        errors: ['${_getFieldDisplayName(fieldName)} has expired'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateUkGracePeriod(dynamic value, String fieldName, int graceDays, Map<String, dynamic>? formData) {
    if (value is! DateTime && value is! String) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    DateTime? date;
    if (value is String) {
      try {
        date = DateTime.parse(value);
      } catch (e) {
        return ValidationResult(isValid: false, errors: ['Invalid date format for ${_getFieldDisplayName(fieldName)}'], warnings: []);
      }
    } else {
      date = value as DateTime;
    }

    final now = DateTime.now();
    
    if (date.isAfter(now)) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    final daysSinceExpiry = now.difference(date).inDays;
    
    // Check if it's a UK passport (grace period applies)
    final countryOfIssue = formData?['country_of_issue']?.toString().toLowerCase();
    final isUkPassport = countryOfIssue?.contains('kingdom') == true || 
                        countryOfIssue?.contains('uk') == true ||
                        countryOfIssue?.contains('gb') == true;

    if (isUkPassport && daysSinceExpiry <= graceDays) {
      return ValidationResult(
        isValid: true,
        errors: [],
        warnings: ['UK passport is expired but within ${graceDays ~/ 30}-month grace period']
      );
    } else if (isUkPassport && daysSinceExpiry > graceDays) {
      return ValidationResult(
        isValid: false,
        errors: ['UK passport is expired beyond the ${graceDays ~/ 30}-month grace period'],
        warnings: []
      );
    } else if (daysSinceExpiry > 0) {
      return ValidationResult(
        isValid: false,
        errors: ['${_getFieldDisplayName(fieldName)} has expired'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateFormat(dynamic value, String fieldName, String format) {
    final stringValue = value.toString();

    switch (format) {
      case '12_digits':
        if (!RegExp(r'^\d{12}$').hasMatch(stringValue)) {
          return ValidationResult(
            isValid: false,
            errors: ['${_getFieldDisplayName(fieldName)} must be exactly 12 digits'],
            warnings: []
          );
        }
        break;
      default:
        break;
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateUkLicenceFormat(dynamic value, String fieldName) {
    final stringValue = value.toString().toUpperCase();

    if (stringValue.length != 16) {
      return ValidationResult(
        isValid: false,
        errors: ['UK driving licence number must be exactly 16 characters'],
        warnings: []
      );
    }

    if (!RegExp(r'^[A-Z]{5}\d{6}[A-Z]{2}\d{2}[A-Z]{1}$').hasMatch(stringValue)) {
      return ValidationResult(
        isValid: false,
        errors: ['UK driving licence number format is invalid'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateExactly4Digits(dynamic value, String fieldName) {
    final stringValue = value.toString();

    if (!RegExp(r'^\d{4}$').hasMatch(stringValue)) {
      return ValidationResult(
        isValid: false,
        errors: ['${_getFieldDisplayName(fieldName)} must be exactly 4 digits'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateMatchApplicantDob(dynamic value, String fieldName, Map<String, dynamic> applicantData) {
    if (value is! DateTime && value is! String) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    DateTime? fieldDate;
    if (value is String) {
      try {
        fieldDate = DateTime.parse(value);
      } catch (e) {
        return ValidationResult(isValid: false, errors: ['Invalid date format'], warnings: []);
      }
    } else {
      fieldDate = value as DateTime;
    }

    final applicantDobString = applicantData['date_of_birth']?.toString();
    if (applicantDobString == null) {
      return ValidationResult(isValid: true, errors: [], warnings: ['Cannot verify against applicant date of birth']);
    }

    DateTime? applicantDob;
    try {
      applicantDob = DateTime.parse(applicantDobString);
    } catch (e) {
      return ValidationResult(isValid: true, errors: [], warnings: ['Cannot parse applicant date of birth']);
    }

    if (fieldDate.year != applicantDob.year ||
        fieldDate.month != applicantDob.month ||
        fieldDate.day != applicantDob.day) {
      return ValidationResult(
        isValid: false,
        errors: ['Date of birth does not match applicant data'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateMatchApplicantAddress(dynamic value, String fieldName, Map<String, dynamic> applicantData) {
    final postcode = value.toString().toUpperCase().replaceAll(' ', '');

    // Get current address postcode
    final currentAddress = applicantData['current_address'] as Map<String, dynamic>?;
    final currentPostcode = currentAddress?['postcode']?.toString().toUpperCase().replaceAll(' ', '');

    if (currentPostcode != null && postcode == currentPostcode) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    return ValidationResult(
      isValid: false,
      errors: ['Postcode does not match applicant\'s current address'],
      warnings: []
    );
  }

  static ValidationResult _validateMatchApplicantAddresses(dynamic value, String fieldName, Map<String, dynamic> applicantData) {
    final postcode = value.toString().toUpperCase().replaceAll(' ', '');

    // Check current address
    final currentAddress = applicantData['current_address'] as Map<String, dynamic>?;
    final currentPostcode = currentAddress?['postcode']?.toString().toUpperCase().replaceAll(' ', '');

    if (currentPostcode != null && postcode == currentPostcode) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    // Check previous addresses
    final previousAddresses = applicantData['previous_addresses'] as List<dynamic>?;
    if (previousAddresses != null) {
      for (final address in previousAddresses) {
        if (address is Map<String, dynamic>) {
          final prevPostcode = address['postcode']?.toString().toUpperCase().replaceAll(' ', '');
          if (prevPostcode != null && postcode == prevPostcode) {
            return ValidationResult(isValid: true, errors: [], warnings: []);
          }
        }
      }
    }

    return ValidationResult(
      isValid: false,
      errors: ['Postcode does not match any address in applicant data'],
      warnings: []
    );
  }

  static ValidationResult _validateMustBeYes(dynamic value, String fieldName) {
    final stringValue = value.toString().toLowerCase();

    if (stringValue != 'yes' && stringValue != 'true' && stringValue != '1') {
      return ValidationResult(
        isValid: false,
        errors: ['${_getFieldDisplayName(fieldName)} must be Yes to proceed'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateBeforeMarch2000(dynamic value, String fieldName) {
    if (value is! DateTime && value is! String) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    DateTime? date;
    if (value is String) {
      try {
        date = DateTime.parse(value);
      } catch (e) {
        return ValidationResult(isValid: false, errors: ['Invalid date format'], warnings: []);
      }
    } else {
      date = value as DateTime;
    }

    final march2000 = DateTime(2000, 3, 1);

    if (date.isAfter(march2000) || date.isAtSameMomentAs(march2000)) {
      return ValidationResult(
        isValid: false,
        errors: ['Paper driving licence must be issued before March 2000 to be valid'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateWithin12MonthsOfBirth(dynamic value, String fieldName, Map<String, dynamic> formData) {
    if (value is! DateTime && value is! String) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    DateTime? registrationDate;
    if (value is String) {
      try {
        registrationDate = DateTime.parse(value);
      } catch (e) {
        return ValidationResult(isValid: false, errors: ['Invalid date format'], warnings: []);
      }
    } else {
      registrationDate = value as DateTime;
    }

    final birthDateValue = formData['date_of_birth'];
    if (birthDateValue == null) {
      return ValidationResult(isValid: true, errors: [], warnings: ['Cannot verify without date of birth']);
    }

    DateTime? birthDate;
    if (birthDateValue is String) {
      try {
        birthDate = DateTime.parse(birthDateValue);
      } catch (e) {
        return ValidationResult(isValid: false, errors: ['Invalid birth date format'], warnings: []);
      }
    } else if (birthDateValue is DateTime) {
      birthDate = birthDateValue;
    } else {
      return ValidationResult(isValid: true, errors: [], warnings: ['Cannot parse birth date']);
    }

    final monthsDifference = (registrationDate.year - birthDate.year) * 12 +
                            (registrationDate.month - birthDate.month);

    if (monthsDifference > 12) {
      return ValidationResult(
        isValid: false,
        errors: ['Birth certificate must be registered within 12 months of birth for Group 1 documents'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateUkIomCi(dynamic value, String fieldName) {
    final stringValue = value.toString().toLowerCase();
    final validLocations = [
      'uk', 'united kingdom', 'england', 'wales', 'scotland', 'northern ireland',
      'isle of man', 'iom', 'jersey', 'guernsey', 'channel islands', 'ci'
    ];

    final isValid = validLocations.any((location) => stringValue.contains(location));

    if (!isValid) {
      return ValidationResult(
        isValid: false,
        errors: ['${_getFieldDisplayName(fieldName)} must be in UK, Isle of Man, or Channel Islands'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateOutsideUk(dynamic value, String fieldName) {
    final stringValue = value.toString().toLowerCase();
    final ukLocations = [
      'uk', 'united kingdom', 'england', 'wales', 'scotland', 'northern ireland',
      'great britain', 'gb'
    ];

    final isUk = ukLocations.any((location) => stringValue.contains(location));

    if (isUk) {
      return ValidationResult(
        isValid: false,
        errors: ['${_getFieldDisplayName(fieldName)} must be outside the UK'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateEeaCountry(dynamic value, String fieldName) {
    final stringValue = value.toString().toLowerCase();
    final eeaCountries = [
      'austria', 'belgium', 'bulgaria', 'croatia', 'cyprus', 'czech republic',
      'denmark', 'estonia', 'finland', 'france', 'germany', 'greece',
      'hungary', 'iceland', 'ireland', 'italy', 'latvia', 'liechtenstein',
      'lithuania', 'luxembourg', 'malta', 'netherlands', 'norway',
      'poland', 'portugal', 'romania', 'slovakia', 'slovenia', 'spain',
      'sweden'
    ];

    final isEea = eeaCountries.any((country) => stringValue.contains(country));

    if (!isEea) {
      return ValidationResult(
        isValid: false,
        errors: ['${_getFieldDisplayName(fieldName)} must be an EEA country'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateUkUtilityNotMobile(dynamic value, String fieldName) {
    final stringValue = value.toString().toLowerCase();
    final mobileProviders = [
      'ee', 'o2', 'vodafone', 'three', '3', 'giffgaff', 'tesco mobile',
      'virgin mobile', 'bt mobile', 'sky mobile', 'id mobile'
    ];

    final isMobile = mobileProviders.any((provider) => stringValue.contains(provider));

    if (isMobile) {
      return ValidationResult(
        isValid: false,
        errors: ['Mobile phone bills are not accepted as utility bills'],
        warnings: []
      );
    }

    return ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult _validateUkLicenceEncoding(dynamic value, String fieldName, Map<String, dynamic> applicantData) {
    final licenceNumber = value.toString().toUpperCase();

    if (licenceNumber.length != 16) {
      return ValidationResult(isValid: true, errors: [], warnings: []); // Format validation handled elsewhere
    }

    final errors = <String>[];
    final warnings = <String>[];

    // Extract surname from licence (first 5 characters)
    final licenceSurname = licenceNumber.substring(0, 5);
    final applicantSurname = applicantData['surname']?.toString().toUpperCase() ?? '';

    if (applicantSurname.isNotEmpty) {
      final expectedSurname = _encodeSurnameForLicence(applicantSurname);
      if (licenceSurname != expectedSurname) {
        errors.add('Surname in licence number does not match provided surname');
      }
    }

    // Extract and validate date of birth encoding
    final dobString = applicantData['date_of_birth']?.toString();
    if (dobString != null) {
      try {
        final dob = DateTime.parse(dobString);
        final gender = applicantData['gender']?.toString().toLowerCase() ?? '';

        // Year (positions 6-7)
        final licenceYear = int.parse(licenceNumber.substring(5, 7));
        final expectedYear = dob.year % 100;

        if (licenceYear != expectedYear) {
          errors.add('Year in licence number does not match date of birth');
        }

        // Month (positions 8-9) - add 50 for females
        final licenceMonth = int.parse(licenceNumber.substring(7, 9));
        final expectedMonth = gender == 'female' ? dob.month + 50 : dob.month;

        if (licenceMonth != expectedMonth) {
          errors.add('Month encoding in licence number does not match date of birth and gender');
        }

        // Day (positions 10-11)
        final licenceDay = int.parse(licenceNumber.substring(9, 11));

        if (licenceDay != dob.day) {
          errors.add('Day in licence number does not match date of birth');
        }
      } catch (e) {
        warnings.add('Could not verify date of birth encoding in licence number');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static String _encodeSurnameForLicence(String surname) {
    // Remove spaces and convert to uppercase
    final cleanSurname = surname.replaceAll(' ', '').toUpperCase();

    // Handle MAC/MC prefixes
    String processedSurname = cleanSurname;
    if (cleanSurname.startsWith('MAC')) {
      processedSurname = 'MC' + cleanSurname.substring(3);
    }

    // Take first 5 characters, pad with 9s if needed
    if (processedSurname.length >= 5) {
      return processedSurname.substring(0, 5);
    } else {
      return processedSurname.padRight(5, '9');
    }
  }
}
