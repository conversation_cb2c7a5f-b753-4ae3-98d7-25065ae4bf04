class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final Map<String, dynamic>? additionalData;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    this.additionalData,
  });

  ValidationResult.success({
    List<String>? warnings,
    Map<String, dynamic>? additionalData,
  }) : this(
          isValid: true,
          errors: const [],
          warnings: warnings ?? const [],
          additionalData: additionalData,
        );

  ValidationResult.failure({
    required List<String> errors,
    List<String>? warnings,
    Map<String, dynamic>? additionalData,
  }) : this(
          isValid: false,
          errors: errors,
          warnings: warnings ?? const [],
          additionalData: additionalData,
        );

  ValidationResult copyWith({
    bool? isValid,
    List<String>? errors,
    List<String>? warnings,
    Map<String, dynamic>? additionalData,
  }) {
    return ValidationResult(
      isValid: isValid ?? this.isValid,
      errors: errors ?? this.errors,
      warnings: warnings ?? this.warnings,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  ValidationResult merge(ValidationResult other) {
    return ValidationResult(
      isValid: isValid && other.isValid,
      errors: [...errors, ...other.errors],
      warnings: [...warnings, ...other.warnings],
      additionalData: {
        ...?additionalData,
        ...?other.additionalData,
      },
    );
  }

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasIssues => hasErrors || hasWarnings;

  @override
  String toString() {
    return 'ValidationResult(isValid: $isValid, errors: $errors, warnings: $warnings)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ValidationResult &&
        other.isValid == isValid &&
        _listEquals(other.errors, errors) &&
        _listEquals(other.warnings, warnings);
  }

  @override
  int get hashCode {
    return isValid.hashCode ^
        errors.hashCode ^
        warnings.hashCode ^
        additionalData.hashCode;
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}
