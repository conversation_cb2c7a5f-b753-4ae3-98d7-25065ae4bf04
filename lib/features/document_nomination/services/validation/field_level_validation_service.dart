import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/validation_result.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/comprehensive_document_validator.dart';

class FieldLevelValidationService {
  static ValidationResult validateField({
    required DocumentDataField field,
    required dynamic value,
    required String documentKey,
    Map<String, dynamic>? applicantData,
    Map<String, dynamic>? allFormData,
  }) {
    // Skip validation if field should not be shown
    if (allFormData != null && !field.shouldShow(allFormData)) {
      return const ValidationResult(isValid: true, errors: [], warnings: []);
    }

    // Use comprehensive validator if validation rules exist
    if (field.validationRules != null && field.validationRules!.isNotEmpty) {
      return ComprehensiveDocumentValidator.validateDocumentField(
        documentKey: documentKey,
        fieldName: field.name,
        fieldValue: value,
        validationRules: field.validationRules!,
        applicantData: applicantData,
        allFormData: allFormData,
      );
    }

    // Basic validation for required fields
    if (field.required && (value == null || value.toString().isEmpty)) {
      return ValidationResult(
        isValid: false,
        errors: ['${field.label} is required'],
        warnings: const [],
      );
    }

    return const ValidationResult(isValid: true, errors: [], warnings: []);
  }

  static ValidationResult validateAllFields({
    required List<DocumentDataField> fields,
    required Map<String, dynamic> formData,
    required String documentKey,
    Map<String, dynamic>? applicantData,
  }) {
    final allErrors = <String>[];
    final allWarnings = <String>[];

    for (final field in fields) {
      // Skip validation for fields that shouldn't be shown
      if (!field.shouldShow(formData)) {
        continue;
      }

      final fieldValue = formData[field.name];
      final result = validateField(
        field: field,
        value: fieldValue,
        documentKey: documentKey,
        applicantData: applicantData,
        allFormData: formData,
      );

      allErrors.addAll(result.errors);
      allWarnings.addAll(result.warnings);
    }

    return ValidationResult(
      isValid: allErrors.isEmpty,
      errors: allErrors,
      warnings: allWarnings,
    );
  }

  static Map<String, ValidationResult> validateFieldsIndividually({
    required List<DocumentDataField> fields,
    required Map<String, dynamic> formData,
    required String documentKey,
    Map<String, dynamic>? applicantData,
  }) {
    final results = <String, ValidationResult>{};

    for (final field in fields) {
      // Skip validation for fields that shouldn't be shown
      if (!field.shouldShow(formData)) {
        results[field.name] = const ValidationResult(isValid: true, errors: [], warnings: []);
        continue;
      }

      final fieldValue = formData[field.name];
      results[field.name] = validateField(
        field: field,
        value: fieldValue,
        documentKey: documentKey,
        applicantData: applicantData,
        allFormData: formData,
      );
    }

    return results;
  }

  static bool shouldShowField({
    required DocumentDataField field,
    required Map<String, dynamic> formData,
  }) {
    return field.shouldShow(formData);
  }

  static List<DocumentDataField> getVisibleFields({
    required List<DocumentDataField> fields,
    required Map<String, dynamic> formData,
  }) {
    return fields.where((field) => field.shouldShow(formData)).toList();
  }

  static ValidationResult validateConditionalLogic({
    required List<DocumentDataField> fields,
    required Map<String, dynamic> formData,
    required String documentKey,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    // Check for conditional field dependencies
    for (final field in fields) {
      if (field.conditional == true && field.conditionalField != null) {
        final dependentFieldValue = formData[field.conditionalField];
        final shouldShow = field.shouldShow(formData);
        
        // If field should show but dependent field is not set appropriately
        if (shouldShow && field.required) {
          final fieldValue = formData[field.name];
          if (fieldValue == null || fieldValue.toString().isEmpty) {
            errors.add('${field.label} is required when ${field.conditionalField} is ${field.conditionalValue}');
          }
        }
      }
    }

    // Document-specific conditional logic
    final documentSpecificResult = _validateDocumentSpecificLogic(documentKey, formData);
    errors.addAll(documentSpecificResult.errors);
    warnings.addAll(documentSpecificResult.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult _validateDocumentSpecificLogic(String documentKey, Map<String, dynamic> formData) {
    final errors = <String>[];
    final warnings = <String>[];

    switch (documentKey) {
      case 'bank_building_society_statement_uk':
        // If UK bank is No, should show non-UK fields
        final isUkBank = formData['is_uk_bank']?.toString().toLowerCase();
        if (isUkBank == 'no' || isUkBank == 'false') {
          if (formData['address_postcode_on_statement']?.toString().isEmpty ?? true) {
            errors.add('Address postcode on statement is required for non-UK banks');
          }
          if (formData['bank_country']?.toString().isEmpty ?? true) {
            errors.add('Bank country is required for non-UK banks');
          }
        }
        break;

      case 'bank_building_society_statement_non_uk':
        // Non-UK bank statement specific validations
        final addressPostcode = formData['address_postcode_on_statement']?.toString();
        if (addressPostcode?.isNotEmpty == true) {
          warnings.add('Verify this postcode matches an address in your address history');
        }
        break;

      case 'immigration_document_visa_work_permit_non_uk':
        // Must be living/working outside UK
        final livingOutsideUk = formData['living_working_outside_uk']?.toString().toLowerCase();
        if (livingOutsideUk != 'yes' && livingOutsideUk != 'true') {
          errors.add('This document can only be used if you will be living and working outside the UK');
        }
        break;

      case 'letter_from_head_teacher_college_principal':
        // Very recent requirement (within 1 month)
        final dateOfLetter = formData['date_of_letter'];
        if (dateOfLetter != null) {
          try {
            final letterDate = dateOfLetter is DateTime ? dateOfLetter : DateTime.parse(dateOfLetter.toString());
            final daysSince = DateTime.now().difference(letterDate).inDays;
            if (daysSince > 30) {
              errors.add('Letter from head teacher must be issued within the last month');
            }
          } catch (e) {
            warnings.add('Could not verify letter date');
          }
        }
        break;

      case 'irish_passport_card':
        // Cannot be used with Irish passport
        warnings.add('This card cannot be used if you have already nominated an Irish passport');
        break;

      case 'birth_certificate_12_months':
        // Group 1 birth certificate - must be registered within 12 months
        final birthDate = formData['date_of_birth'];
        final registrationDate = formData['date_of_issue_registration'];
        
        if (birthDate != null && registrationDate != null) {
          try {
            final birth = birthDate is DateTime ? birthDate : DateTime.parse(birthDate.toString());
            final registration = registrationDate is DateTime ? registrationDate : DateTime.parse(registrationDate.toString());
            
            final monthsDiff = (registration.year - birth.year) * 12 + (registration.month - birth.month);
            if (monthsDiff > 12) {
              errors.add('For Group 1 documents, birth certificate must be registered within 12 months of birth');
            }
          } catch (e) {
            warnings.add('Could not verify registration timeframe');
          }
        }
        break;

      case 'driving_licence_paper_uk_pre_2000':
        // Must be issued before March 2000
        final issueDate = formData['issue_date'];
        if (issueDate != null) {
          try {
            final issue = issueDate is DateTime ? issueDate : DateTime.parse(issueDate.toString());
            final march2000 = DateTime(2000, 3, 1);
            
            if (issue.isAfter(march2000) || issue.isAtSameMomentAs(march2000)) {
              errors.add('Paper driving licence must be issued before March 2000 to be valid');
            }
          } catch (e) {
            warnings.add('Could not verify issue date');
          }
        }
        break;

      case 'biometric_residence_permit_uk':
        // Special expiry rules for indefinite leave
        final immigrationStatus = formData['immigration_status']?.toString().toLowerCase();
        final expiryDate = formData['date_of_expiry'];
        
        if (expiryDate != null && immigrationStatus != null) {
          try {
            final expiry = expiryDate is DateTime ? expiryDate : DateTime.parse(expiryDate.toString());
            final now = DateTime.now();
            
            if (expiry.isBefore(now)) {
              final daysSinceExpiry = now.difference(expiry).inDays;
              
              if (immigrationStatus.contains('indefinite') || immigrationStatus.contains('no time limit')) {
                if (daysSinceExpiry <= 540) { // 18 months
                  warnings.add('BRP with indefinite leave is expired but within 18-month grace period');
                } else {
                  errors.add('BRP with indefinite leave is expired beyond the 18-month grace period');
                }
              } else {
                errors.add('BRP with limited leave has expired and must be current');
              }
            }
          } catch (e) {
            warnings.add('Could not verify BRP expiry rules');
          }
        }
        break;

      default:
        break;
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static String getFieldValidationMessage(String documentKey, String fieldName, dynamic value) {
    // Provide helpful validation messages for specific fields
    switch ('${documentKey}_$fieldName') {
      case 'driving_licence_uk_photocard_licence_number':
        return 'UK driving licence numbers encode your surname, date of birth, and gender. We will verify this matches your application details.';
      
      case 'passport_any_date_of_expiry':
        return 'UK passports can be accepted up to 6 months past expiry. Other passports must be current.';
      
      case 'biometric_residence_permit_uk_date_of_expiry':
        return 'BRP with indefinite leave can be accepted up to 18 months past expiry. Limited leave must be current.';
      
      case 'birth_certificate_12_months_date_of_issue_registration':
        return 'For Group 1 documents, birth must be registered within 12 months of birth.';
      
      case 'utility_bill_uk_not_mobile_provider_name':
        return 'Mobile phone bills are not accepted. Only gas, electricity, water, internet, or waste bills.';
      
      case 'letter_from_head_teacher_college_principal_date_of_letter':
        return 'Letter must be very recent - issued within the last month.';
      
      default:
        return '';
    }
  }
}
