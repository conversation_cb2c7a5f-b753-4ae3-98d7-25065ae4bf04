import 'package:SolidCheck/features/document_nomination/services/validation/validation_result.dart';

class UkDrivingLicenceValidator {
  static ValidationResult validateLicenceNumber({
    required String licenceNumber,
    required String surname,
    required String firstName,
    required String? middleName,
    required DateTime dateOfBirth,
    required String gender,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (licenceNumber.isEmpty) {
      errors.add('Licence number is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    final cleanLicenceNumber = licenceNumber.replaceAll(' ', '').toUpperCase();
    
    if (cleanLicenceNumber.length != 16) {
      errors.add('UK driving licence number must be exactly 16 characters');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    if (!RegExp(r'^[A-Z0-9]{16}$').hasMatch(cleanLicenceNumber)) {
      errors.add('Licence number contains invalid characters');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    final validationResult = _validateEnglishWelshScottishLicence(
      cleanLicenceNumber,
      surname,
      firstName,
      middleName,
      dateOfBirth,
      gender,
    );

    errors.addAll(validationResult.errors);
    warnings.addAll(validationResult.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult _validateEnglishWelshScottishLicence(
    String licenceNumber,
    String surname,
    String firstName,
    String? middleName,
    DateTime dateOfBirth,
    String gender,
  ) {
    final errors = <String>[];
    final warnings = <String>[];

    try {
      final surnameSection = licenceNumber.substring(0, 5);
      final yearSection = licenceNumber.substring(5, 7);
      final monthSection = licenceNumber.substring(7, 9);
      final daySection = licenceNumber.substring(9, 11);
      final yearCheckSection = licenceNumber.substring(11, 12);
      final initialsSection = licenceNumber.substring(12, 14);

      final expectedSurname = _formatSurnameForLicence(surname);
      if (surnameSection != expectedSurname) {
        errors.add('Surname in licence number does not match provided surname');
      }

      final expectedYear = dateOfBirth.year.toString().substring(2);
      if (yearSection != expectedYear) {
        errors.add('Year of birth in licence number does not match provided date of birth');
      }

      final expectedMonth = _formatMonthForLicence(dateOfBirth.month, gender.toLowerCase() == 'female');
      if (monthSection != expectedMonth) {
        errors.add('Month of birth in licence number does not match provided date of birth and gender');
      }

      final expectedDay = dateOfBirth.day.toString().padLeft(2, '0');
      if (daySection != expectedDay) {
        errors.add('Day of birth in licence number does not match provided date of birth');
      }

      final expectedYearCheck = (dateOfBirth.year % 10).toString();
      if (yearCheckSection != expectedYearCheck) {
        errors.add('Year check digit in licence number does not match provided date of birth');
      }

      final expectedInitials = _formatInitialsForLicence(firstName, middleName);
      if (initialsSection != expectedInitials) {
        if (middleName != null && middleName.isNotEmpty) {
          errors.add('Initials in licence number do not match provided first name and middle name');
        } else {
          warnings.add('Initials in licence number suggest a middle name may be present');
        }
      }

    } catch (e) {
      errors.add('Invalid licence number format');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static String _formatSurnameForLicence(String surname) {
    String formatted = surname.toUpperCase().replaceAll(' ', '');
    
    if (formatted.startsWith('MAC') || formatted.startsWith('MC')) {
      formatted = 'MC' + formatted.substring(formatted.startsWith('MAC') ? 3 : 2);
    }
    
    formatted = formatted.replaceAll(RegExp(r'[^A-Z]'), '');
    
    if (formatted.length >= 5) {
      return formatted.substring(0, 5);
    } else {
      return formatted.padRight(5, '9');
    }
  }

  static String _formatMonthForLicence(int month, bool isFemale) {
    if (isFemale) {
      return (month + 50).toString().padLeft(2, '0');
    } else {
      return month.toString().padLeft(2, '0');
    }
  }

  static String _formatInitialsForLicence(String firstName, String? middleName) {
    final firstInitial = firstName.isNotEmpty ? firstName[0].toUpperCase() : '9';
    final middleInitial = (middleName != null && middleName.isNotEmpty) 
        ? middleName[0].toUpperCase() 
        : '9';
    
    return firstInitial + middleInitial;
  }

  static ValidationResult validateLicenceExpiry({
    required DateTime? expiryDate,
    required DateTime? dateOfBirth,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (expiryDate == null) {
      errors.add('Expiry date is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    final now = DateTime.now();
    if (expiryDate.isBefore(now)) {
      errors.add('Driving licence has expired');
    }

    if (dateOfBirth != null) {
      final expectedExpiryAge = dateOfBirth.year < 1954 ? 70 : 70;
      final expectedExpiry = DateTime(dateOfBirth.year + expectedExpiryAge, dateOfBirth.month, dateOfBirth.day - 1);
      
      final daysDifference = expiryDate.difference(expectedExpiry).inDays.abs();
      if (daysDifference > 1) {
        warnings.add('Expiry date does not match expected date based on 70th birthday rule');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateNorthernIrelandLicence({
    required String licenceNumber,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (licenceNumber.isEmpty) {
      errors.add('Licence number is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    final cleanLicenceNumber = licenceNumber.replaceAll(' ', '');
    
    if (cleanLicenceNumber.length < 8 || cleanLicenceNumber.length > 10) {
      errors.add('Northern Ireland driving licence number should be 8-10 characters');
    }

    if (!RegExp(r'^[0-9]+$').hasMatch(cleanLicenceNumber)) {
      errors.add('Northern Ireland licence number should contain only numbers');
    }

    warnings.add('Northern Ireland licence numbers cannot be validated against personal details');

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateIsleOfManJerseyLicence({
    required String licenceNumber,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (licenceNumber.isEmpty) {
      errors.add('Licence number is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    warnings.add('Isle of Man and Jersey licence numbers cannot be validated against personal details');

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validatePhotoLicence({
    required DateTime? issueDate,
    required DateTime? expiryDate,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (issueDate == null) {
      errors.add('Issue date is required');
    } else {
      final now = DateTime.now();
      if (issueDate.isAfter(now)) {
        errors.add('Issue date cannot be in the future');
      }
    }

    if (expiryDate == null) {
      errors.add('Expiry date is required');
    } else {
      final now = DateTime.now();
      if (expiryDate.isBefore(now)) {
        errors.add('Photocard driving licence has expired');
      }
    }

    warnings.add('Examine the licence for evidence of photo tampering or amendment of printed details');

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validatePaperLicence({
    required DateTime? issueDate,
    required DateTime? expiryDate,
    required DateTime? dateOfBirth,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (issueDate == null) {
      errors.add('Issue date is required');
    } else {
      final march2000 = DateTime(2000, 3, 1);
      if (issueDate.isAfter(march2000)) {
        errors.add('Paper driving licence must be issued before March 2000 to be valid');
      }
    }

    warnings.addAll([
      'Remove document from plastic wallet and check printed on both sides',
      'Check for watermark visible when held up to light',
      'Ensure no punctuation marks in name or address',
      'Valid To date should be day before bearer\'s 70th birthday',
    ]);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
}
