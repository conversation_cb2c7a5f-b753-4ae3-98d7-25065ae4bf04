import 'package:SolidCheck/features/document_nomination/services/validation/validation_result.dart';

class BirthCertificateValidator {
  static ValidationResult validateGroup1BirthCertificate({
    required DateTime? dateOfBirth,
    required DateTime? registrationDate,
    required String placeOfBirth,
    required String? certificateNumber,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (dateOfBirth == null) {
      errors.add('Date of birth is required');
    }

    if (registrationDate == null) {
      errors.add('Registration date is required');
    }

    if (dateOfBirth != null && registrationDate != null) {
      final daysDifference = registrationDate.difference(dateOfBirth).inDays;
      
      if (daysDifference < 0) {
        errors.add('Registration date cannot be before date of birth');
      } else if (daysDifference > 365) {
        errors.add('Birth certificate must be registered within 12 months of birth for Group 1 documents');
      }
    }

    if (placeOfBirth.isEmpty) {
      errors.add('Place of birth is required');
    } else {
      final validCountries = ['UK', 'United Kingdom', 'England', 'Wales', 'Scotland', 'Northern Ireland', 'Isle of Man', 'Jersey', 'Guernsey'];
      final isValidPlace = validCountries.any((country) => 
        placeOfBirth.toLowerCase().contains(country.toLowerCase()));
      
      if (!isValidPlace && !placeOfBirth.toLowerCase().contains('embassy') && !placeOfBirth.toLowerCase().contains('consulate')) {
        errors.add('Birth certificate must be issued in UK/IoM/CI or by UK authorities overseas');
      }
    }

    warnings.addAll([
      'Check certificate uses high grade paper',
      'Verify watermark is visible when held up to light',
      'Check for signs of tampering, liquid paper, or overwriting',
      'Ensure no spelling mistakes are present',
      'Only surname should be in upper case, not forename(s)',
      'Date should show day and month in words, year in figures',
    ]);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateGroup2aBirthCertificate({
    required DateTime? dateOfBirth,
    required DateTime? registrationDate,
    required String placeOfBirth,
    required String? certificateNumber,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (dateOfBirth == null) {
      errors.add('Date of birth is required');
    }

    if (registrationDate == null) {
      errors.add('Registration date is required');
    }

    if (dateOfBirth != null && registrationDate != null) {
      if (registrationDate.isBefore(dateOfBirth)) {
        errors.add('Registration date cannot be before date of birth');
      }
    }

    if (placeOfBirth.isEmpty) {
      errors.add('Place of birth is required');
    } else {
      final validCountries = ['UK', 'United Kingdom', 'England', 'Wales', 'Scotland', 'Northern Ireland', 'Isle of Man', 'Jersey', 'Guernsey'];
      final isValidPlace = validCountries.any((country) => 
        placeOfBirth.toLowerCase().contains(country.toLowerCase()));
      
      if (!isValidPlace && !placeOfBirth.toLowerCase().contains('embassy') && !placeOfBirth.toLowerCase().contains('consulate')) {
        errors.add('Birth certificate must be issued in UK/IoM/CI or by UK authorities overseas');
      }
    }

    warnings.addAll([
      'Post-birth certificates confirm identity but may not show corrections',
      'Check certificate uses high grade paper',
      'Verify watermark is visible when held up to light',
      'Check for signs of tampering or alterations',
      'Verify certificate format is appropriate for year of registration',
    ]);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateCertificateFormat({
    required String? certificateNumber,
    required DateTime? registrationDate,
    required String fullNameOnCertificate,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (fullNameOnCertificate.isEmpty) {
      errors.add('Full name on certificate is required');
    } else {
      final nameParts = fullNameOnCertificate.split(' ');
      if (nameParts.length < 2) {
        warnings.add('Name should include both forename and surname');
      }

      final hasUpperCaseForename = nameParts.first.toUpperCase() == nameParts.first;
      if (hasUpperCaseForename && nameParts.first.length > 1) {
        warnings.add('Only surname should be in upper case, not forename(s)');
      }
    }

    if (registrationDate != null) {
      final year = registrationDate.year;
      if (year < 1837) {
        warnings.add('Civil registration began in 1837 - verify certificate authenticity');
      }
    }

    warnings.addAll([
      'Check spacing between particulars is regular',
      'Verify all particulars are properly aligned',
      'Ensure characters are consistent in size and shape',
      'Check handwriting flows naturally',
      'Verify changes are consistent throughout document',
      'Check for UV light reactions indicating alterations',
    ]);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateAdoptionCertificate({
    required DateTime? adoptionDate,
    required String issuingAuthority,
    required String? certificateNumber,
    required String? adoptedName,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (adoptionDate == null) {
      errors.add('Date of adoption is required');
    } else {
      final now = DateTime.now();
      if (adoptionDate.isAfter(now)) {
        errors.add('Adoption date cannot be in the future');
      }
    }

    if (issuingAuthority.isEmpty) {
      errors.add('Issuing authority is required');
    } else {
      final validAuthorities = ['UK', 'United Kingdom', 'England', 'Wales', 'Scotland', 'Northern Ireland', 'Jersey', 'Guernsey', 'Court', 'Registry'];
      final isValidAuthority = validAuthorities.any((authority) => 
        issuingAuthority.toLowerCase().contains(authority.toLowerCase()));
      
      if (!isValidAuthority) {
        warnings.add('Verify issuing authority is a UK or Channel Islands court/authority');
      }
    }

    if (certificateNumber?.isEmpty ?? true) {
      warnings.add('Certificate number helps verify document authenticity');
    }

    warnings.addAll([
      'UK/CI adoption certificates have no expiry date',
      'Verify certificate matches applicant\'s current or previous name',
      'Check adoption date is consistent with applicant\'s age',
    ]);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  static ValidationResult validateDateFormat({
    required String dateString,
    required String fieldName,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (dateString.isEmpty) {
      errors.add('$fieldName is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    final hasWordsForDayMonth = RegExp(r'\b(January|February|March|April|May|June|July|August|September|October|November|December)\b', caseSensitive: false).hasMatch(dateString);
    final hasNumbersForYear = RegExp(r'\b\d{4}\b').hasMatch(dateString);

    if (!hasWordsForDayMonth) {
      warnings.add('Date should show day and month in words (e.g., "First January")');
    }

    if (!hasNumbersForYear) {
      warnings.add('Date should show year in figures (e.g., "1990")');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
}
