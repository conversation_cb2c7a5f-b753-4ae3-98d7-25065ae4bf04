import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';

class ApplicantContext {
  final int age;
  final String nationality;
  final bool isUkNational;
  final bool isEuNational;
  final List<String> addressCountries;
  final bool hasUkAddresses;
  final bool hasIrishAddresses;
  final bool hasEuAddresses;
  final bool isStudent;
  final bool isYoungApplicant;
  final String primaryResidenceCountry;
  final int yearsInUk;
  final bool isRecentImmigrant;

  const ApplicantContext({
    required this.age,
    required this.nationality,
    required this.isUkNational,
    required this.isEuNational,
    required this.addressCountries,
    required this.hasUkAddresses,
    required this.hasIrishAddresses,
    required this.hasEuAddresses,
    required this.isStudent,
    required this.isYoungApplicant,
    required this.primaryResidenceCountry,
    required this.yearsInUk,
    required this.isRecentImmigrant,
  });
}

class SmartApplicantContextService {
  static ApplicantContext analyzeApplicant(ApplicantDetailsData applicantData) {
    final age = _calculateAge(applicantData.dateOfBirth);
    final nationality = applicantData.nationality?.toLowerCase() ?? '';
    final addressCountries = _extractAddressCountries(applicantData);
    
    return ApplicantContext(
      age: age,
      nationality: nationality,
      isUkNational: _isUkNational(nationality),
      isEuNational: _isEuNational(nationality),
      addressCountries: addressCountries,
      hasUkAddresses: _hasUkAddresses(addressCountries),
      hasIrishAddresses: _hasIrishAddresses(addressCountries),
      hasEuAddresses: _hasEuAddresses(addressCountries),
      isStudent: _isStudent(applicantData),
      isYoungApplicant: age >= 16 && age <= 19,
      primaryResidenceCountry: _getPrimaryResidenceCountry(addressCountries),
      yearsInUk: _calculateYearsInUk(applicantData),
      isRecentImmigrant: _isRecentImmigrant(applicantData),
    );
  }

  static int _calculateAge(DateTime dateOfBirth) {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  static bool _isUkNational(String nationality) {
    final ukNationalities = [
      'british', 'uk', 'united kingdom', 'english', 'welsh', 'scottish', 
      'northern irish', 'british citizen', 'uk citizen'
    ];
    return ukNationalities.any((uk) => nationality.contains(uk));
  }

  static bool _isEuNational(String nationality) {
    final euNationalities = [
      'irish', 'german', 'french', 'italian', 'spanish', 'dutch', 'belgian',
      'portuguese', 'polish', 'czech', 'hungarian', 'romanian', 'bulgarian',
      'croatian', 'slovenian', 'slovakian', 'estonian', 'latvian', 'lithuanian',
      'finnish', 'swedish', 'danish', 'austrian', 'greek', 'cypriot', 'maltese',
      'luxembourgish'
    ];
    return euNationalities.any((eu) => nationality.contains(eu));
  }

  static List<String> _extractAddressCountries(ApplicantDetailsData applicantData) {
    final countries = <String>[];
    
    if (applicantData.currentAddress?.country != null) {
      countries.add(applicantData.currentAddress!.country!.toLowerCase());
    }
    
    // Add previous addresses if available
    // This would need to be implemented based on your address history structure
    
    return countries.toSet().toList();
  }

  static bool _hasUkAddresses(List<String> addressCountries) {
    final ukCountries = ['uk', 'united kingdom', 'gb', 'great britain', 'england', 'wales', 'scotland', 'northern ireland'];
    return addressCountries.any((country) => 
      ukCountries.any((uk) => country.contains(uk)));
  }

  static bool _hasIrishAddresses(List<String> addressCountries) {
    return addressCountries.any((country) => 
      country.contains('ireland') && !country.contains('northern'));
  }

  static bool _hasEuAddresses(List<String> addressCountries) {
    final euCountries = [
      'ireland', 'germany', 'france', 'italy', 'spain', 'netherlands', 'belgium',
      'portugal', 'poland', 'czech', 'hungary', 'romania', 'bulgaria',
      'croatia', 'slovenia', 'slovakia', 'estonia', 'latvia', 'lithuania',
      'finland', 'sweden', 'denmark', 'austria', 'greece', 'cyprus', 'malta',
      'luxembourg'
    ];
    return addressCountries.any((country) => 
      euCountries.any((eu) => country.contains(eu)));
  }

  static bool _isStudent(ApplicantDetailsData applicantData) {
    // This would check if the applicant is currently in education
    // Implementation depends on your form structure
    return false; // Placeholder
  }

  static String _getPrimaryResidenceCountry(List<String> addressCountries) {
    if (addressCountries.isEmpty) return 'unknown';
    return addressCountries.first; // Most recent address
  }

  static int _calculateYearsInUk(ApplicantDetailsData applicantData) {
    // This would calculate how long the applicant has been in the UK
    // Implementation depends on your address history structure
    return 0; // Placeholder
  }

  static bool _isRecentImmigrant(ApplicantDetailsData applicantData) {
    // This would check if the applicant is a recent immigrant (e.g., < 2 years in UK)
    // Implementation depends on your address history structure
    return false; // Placeholder
  }

  static List<String> getDocumentAvailabilityReasons(
    String documentKey, 
    ApplicantContext context
  ) {
    final reasons = <String>[];

    switch (documentKey) {
      case 'irish_passport_card':
        if (!context.hasIrishAddresses && !context.nationality.contains('irish')) {
          reasons.add('Irish Passport Card is only available to Irish citizens or residents');
        }
        break;

      case 'letter_from_head_teacher_college_principal':
        if (!context.isYoungApplicant) {
          reasons.add('Letter from Head Teacher is only available for applicants aged 16-19');
        }
        if (!context.isStudent) {
          reasons.add('Letter from Head Teacher requires current enrollment in education');
        }
        break;

      case 'eea_national_id_card':
        if (!context.isEuNational && !context.hasEuAddresses) {
          reasons.add('EEA National ID Card is only available to EEA nationals');
        }
        break;

      case 'driving_licence_photocard_non_uk':
        if (context.isUkNational && !context.hasEuAddresses) {
          reasons.add('Non-UK driving licence not applicable for UK nationals without overseas residence');
        }
        break;

      case 'bank_building_society_statement_non_uk':
        if (!context.hasEuAddresses && context.primaryResidenceCountry.contains('uk')) {
          reasons.add('Non-UK bank statements only applicable for overseas residents');
        }
        break;

      case 'immigration_document_visa_work_permit_non_uk':
        if (context.isUkNational) {
          reasons.add('Non-UK immigration documents not applicable for UK nationals');
        }
        break;
    }

    return reasons;
  }

  static bool isDocumentAvailable(String documentKey, ApplicantContext context) {
    return getDocumentAvailabilityReasons(documentKey, context).isEmpty;
  }

  static String getSmartDocumentName(String originalName, String documentKey) {
    // Convert technical names to user-friendly names
    return originalName
        .replaceAll('(UK/CI)', '(UK)')
        .replaceAll('(UK, IoM, CI)', '(UK)')
        .replaceAll('Isle of Man', 'UK')
        .replaceAll('Channel Islands', 'UK')
        .replaceAll('IoM', 'UK')
        .replaceAll('CI', 'UK')
        .replaceAll('(non-UK)', '(NON-UK)')
        .replaceAll('outside UK', '(NON-UK)')
        .replaceAll('(UK/CI)', '(UK)')
        .replaceAll('UK and Channel Islands', 'UK');
  }

  static List<String> getDocumentRecommendations(ApplicantContext context) {
    final recommendations = <String>[];

    if (context.isUkNational) {
      recommendations.addAll([
        'UK Passport is the strongest form of ID',
        'UK Driving Licence is widely accepted and easy to verify',
        'Recent utility bills confirm your current address',
      ]);
    }

    if (context.isYoungApplicant) {
      recommendations.add('Consider asking your school/college for a letter if other documents are unavailable');
    }

    if (context.isRecentImmigrant) {
      recommendations.addAll([
        'Your immigration documents (BRP, e-Visa) are valuable for identity verification',
        'Bank statements help establish your UK residence',
      ]);
    }

    return recommendations;
  }

  static String getRouteGuidance(ApplicantContext context) {
    if (context.isUkNational) {
      return 'Route 1 is recommended: Use your UK passport or driving licence plus one additional document';
    } else if (context.isRecentImmigrant) {
      return 'Route 1 is recommended: Use your passport or BRP plus one additional document';
    } else {
      return 'Route 2 may be easier: Use government documents plus two additional documents';
    }
  }
}
