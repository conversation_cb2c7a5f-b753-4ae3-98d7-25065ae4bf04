import 'dart:convert';

import 'document_nomination.dart';
import 'document_type.dart';
import 'route_requirement.dart';

class ApplicantContext {
  final String nationality;
  final String currentAddressCountry;
  final bool isUkNational;
  final bool isUkResident;
  final String workType;
  final String productCode;

  ApplicantContext({
    required this.nationality,
    required this.currentAddressCountry,
    required this.isUkNational,
    required this.isUkResident,
    required this.workType,
    required this.productCode,
  });

  factory ApplicantContext.fromJson(Map<String, dynamic> json) {
    return ApplicantContext(
      nationality: json['nationality'] ?? 'Unknown',
      currentAddressCountry: json['current_address_country'] ?? 'Unknown',
      isUkNational: json['is_uk_national'] ?? false,
      isUkResident: json['is_uk_resident'] ?? false,
      workType: json['work_type'] ?? '',
      productCode: json['product_code'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'nationality': nationality,
      'current_address_country': currentAddressCountry,
      'is_uk_national': isUkNational,
      'is_uk_resident': isUkResident,
      'work_type': workType,
      'product_code': productCode,
    };
  }
}

class ApplicationInfo {
  final int id;
  final String productCode;
  final String productName;
  final int applicantId;
  final String applicantName;
  final String status;

  ApplicationInfo({
    required this.id,
    required this.productCode,
    required this.productName,
    required this.applicantId,
    required this.applicantName,
    required this.status,
  });

  factory ApplicationInfo.fromJson(Map<String, dynamic> json) {
    return ApplicationInfo(
      id: json['id'] ?? 0,
      productCode: json['product_code'] ?? '',
      productName: json['product_name'] ?? '',
      applicantId: json['applicant_id'] ?? 0,
      applicantName: json['applicant_name'] ?? '',
      status: json['status'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_code': productCode,
      'product_name': productName,
      'applicant_id': applicantId,
      'applicant_name': applicantName,
      'status': status,
    };
  }
}

class RoutingInfo {
  final int recommendedRoute;
  final List<int> availableRoutes;
  final Map<String, RouteRequirement> routeRequirements;

  RoutingInfo({
    required this.recommendedRoute,
    required this.availableRoutes,
    required this.routeRequirements,
  });

  factory RoutingInfo.fromJson(Map<String, dynamic> json) {
    final routeRequirementsMap = <String, RouteRequirement>{};
    final routeRequirementsJson = json['route_requirements'] as Map<String, dynamic>? ?? {};

    for (final entry in routeRequirementsJson.entries) {
      // Ensure proper type casting
      final requirementData = entry.value;
      if (requirementData is Map<String, dynamic>) {
        routeRequirementsMap[entry.key] = RouteRequirement.fromJson(requirementData);
      } else if (requirementData is Map) {
        // Convert Map<String, Object?> to Map<String, dynamic>
        final convertedData = Map<String, dynamic>.from(requirementData);
        routeRequirementsMap[entry.key] = RouteRequirement.fromJson(convertedData);
      }
    }

    return RoutingInfo(
      recommendedRoute: json['recommended_route'] ?? 1,
      availableRoutes: List<int>.from(json['available_routes'] ?? []),
      routeRequirements: routeRequirementsMap,
    );
  }

  Map<String, dynamic> toJson() {
    final routeRequirementsJson = <String, dynamic>{};
    for (final entry in routeRequirements.entries) {
      routeRequirementsJson[entry.key] = entry.value.toJson();
    }

    return {
      'recommended_route': recommendedRoute,
      'available_routes': availableRoutes,
      'route_requirements': routeRequirementsJson,
    };
  }
}

class DocumentGroup {
  final String groupName;
  final int documentCount;
  final List<DocumentType> documents;

  DocumentGroup({
    required this.groupName,
    required this.documentCount,
    required this.documents,
  });

  factory DocumentGroup.fromJson(Map<String, dynamic> json) {
    return DocumentGroup(
      groupName: json['group_name'] ?? '',
      documentCount: json['document_count'] ?? 0,
      documents: (json['documents'] as List<dynamic>?)
              ?.map((item) {
                if (item is Map<String, dynamic>) {
                  return DocumentType.fromJson(item);
                } else if (item is Map) {
                  // Convert Map<String, Object?> to Map<String, dynamic>
                  return DocumentType.fromJson(Map<String, dynamic>.from(item));
                } else {
                  throw Exception('Invalid document data type: ${item.runtimeType}');
                }
              })
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'group_name': groupName,
      'document_count': documentCount,
      'documents': documents.map((doc) => doc.toJson()).toList(),
    };
  }
}

class DocumentsInfo {
  final Map<String, DocumentGroup> availableByGroup;
  final int totalAvailable;

  DocumentsInfo({
    required this.availableByGroup,
    required this.totalAvailable,
  });

  factory DocumentsInfo.fromJson(Map<String, dynamic> json) {
    final availableByGroupMap = <String, DocumentGroup>{};
    final availableByGroupJson = json['available_by_group'] as Map<String, dynamic>? ?? {};

    for (final entry in availableByGroupJson.entries) {
      // Ensure proper type casting
      final groupData = entry.value;
      if (groupData is Map<String, dynamic>) {
        availableByGroupMap[entry.key] = DocumentGroup.fromJson(groupData);
      } else if (groupData is Map) {
        // Convert Map<String, Object?> to Map<String, dynamic>
        final convertedData = Map<String, dynamic>.from(groupData);
        availableByGroupMap[entry.key] = DocumentGroup.fromJson(convertedData);
      }
    }

    return DocumentsInfo(
      availableByGroup: availableByGroupMap,
      totalAvailable: json['total_available'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    final availableByGroupJson = <String, dynamic>{};
    for (final entry in availableByGroup.entries) {
      availableByGroupJson[entry.key] = entry.value.toJson();
    }

    return {
      'available_by_group': availableByGroupJson,
      'total_available': totalAvailable,
    };
  }
}

class AvailableDocumentsResponse {
  final bool success;
  final AvailableDocumentsData data;

  AvailableDocumentsResponse({
    required this.success,
    required this.data,
  });

  factory AvailableDocumentsResponse.fromJson(Map<String, dynamic> json) {
    return AvailableDocumentsResponse(
      success: json['success'] ?? false,
      data: AvailableDocumentsData.fromJson(json['data'] ?? {}),
    );
  }

  factory AvailableDocumentsResponse.fromRawJson(String str) =>
      AvailableDocumentsResponse.fromJson(json.decode(str));

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.toJson(),
    };
  }

  String toRawJson() => json.encode(toJson());

  @override
  String toString() {
    return 'AvailableDocumentsResponse(success: $success, data: $data)';
  }
}

class AvailableDocumentsData {
  final ApplicationInfo application;
  final ApplicantContext applicantContext;
  final RoutingInfo routing;
  final DocumentsInfo documents;
  final List<DocumentNomination> currentNominations;

  AvailableDocumentsData({
    required this.application,
    required this.applicantContext,
    required this.routing,
    required this.documents,
    required this.currentNominations,
  });

  // Legacy getters for backward compatibility
  int get recommendedRoute => routing.recommendedRoute;
  List<int> get availableRoutes => routing.availableRoutes;
  Map<String, RouteRequirement> get routeRequirements => routing.routeRequirements;

  // Helper method to find document type by ID
  DocumentType? getDocumentTypeById(int documentTypeId) {
    for (final group in documents.availableByGroup.values) {
      for (final document in group.documents) {
        if (document.id == documentTypeId) {
          return document;
        }
      }
    }
    return null;
  }
  Map<String, List<DocumentType>> get availableDocuments {
    final result = <String, List<DocumentType>>{};
    for (final entry in documents.availableByGroup.entries) {
      result[entry.key] = entry.value.documents;
    }
    return result;
  }

  factory AvailableDocumentsData.fromJson(Map<String, dynamic> json) {
    return AvailableDocumentsData(
      application: ApplicationInfo.fromJson(json['application'] ?? {}),
      applicantContext: ApplicantContext.fromJson(json['applicant_context'] ?? {}),
      routing: RoutingInfo.fromJson(json['routing'] ?? {}),
      documents: DocumentsInfo.fromJson(json['documents'] ?? {}),
      currentNominations: (json['documents']?['current_nominations'] as List<dynamic>?)
              ?.map((item) {
                try {
                  return DocumentNomination.fromJson(item);
                } catch (e) {
                  print('Error parsing DocumentNomination: $e');
                  print('Item data: $item');
                  rethrow;
                }
              })
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'application': application.toJson(),
      'applicant_context': applicantContext.toJson(),
      'routing': routing.toJson(),
      'documents': documents.toJson(),
      'current_nominations': currentNominations.map((nom) => nom.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'AvailableDocumentsData(recommendedRoute: $recommendedRoute, availableRoutes: $availableRoutes)';
  }
}
