import 'dart:convert';
import 'package:SolidCheck/features/document_nomination/services/field_type_mapping_service.dart';

class DocumentType {
  final String key;
  final String name;
  final String documentGroup;
  final bool requiresPhoto;
  final bool confirmsAddress;
  final List<String> applicableCountries;
  final List<DocumentDataField> dataFields;
  final String? notes;
  final Map<String, dynamic>? smartFiltering;

  DocumentType({
    required this.key,
    required this.name,
    required this.documentGroup,
    required this.requiresPhoto,
    required this.confirmsAddress,
    required this.applicableCountries,
    required this.dataFields,
    this.notes,
    this.smartFiltering,
  });

  // Legacy getter for backward compatibility - use a stable hash
  int get id {
    // Create a stable hash from the key string using djb2 algorithm
    int hash = 5381;
    for (int i = 0; i < key.length; i++) {
      hash = ((hash << 5) + hash + key.codeUnitAt(i)) & 0xffffffff;
    }
    // Ensure we always return a positive integer
    return hash.abs();
  }

  factory DocumentType.fromJson(Map<String, dynamic> json) {
    final dataFieldsList = <DocumentDataField>[];
    final dataFieldsJson = json['data_fields'] as List<dynamic>? ?? [];

    for (final fieldJson in dataFieldsJson) {
      dataFieldsList.add(DocumentDataField.fromJson(fieldJson));
    }

    return DocumentType(
      key: json['key'] ?? '',
      name: json['name'] ?? '',
      documentGroup: json['document_group'] ?? '',
      requiresPhoto: json['requires_photo'] ?? false,
      confirmsAddress: json['confirms_address'] ?? false,
      applicableCountries: List<String>.from(json['applicable_countries'] ?? []),
      dataFields: dataFieldsList,
      notes: json['notes'] as String?,
      smartFiltering: json['smart_filtering'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'name': name,
      'document_group': documentGroup,
      'requires_photo': requiresPhoto,
      'confirms_address': confirmsAddress,
      'applicable_countries': applicableCountries,
      'data_fields': dataFields.map((field) => field.toJson()).toList(),
      if (notes != null) 'notes': notes,
      if (smartFiltering != null) 'smart_filtering': smartFiltering,
    };
  }

  DocumentType copyWith({
    String? key,
    String? name,
    String? documentGroup,
    bool? requiresPhoto,
    bool? confirmsAddress,
    List<String>? applicableCountries,
    List<DocumentDataField>? dataFields,
    String? notes,
    Map<String, dynamic>? smartFiltering,
  }) {
    return DocumentType(
      key: key ?? this.key,
      name: name ?? this.name,
      documentGroup: documentGroup ?? this.documentGroup,
      requiresPhoto: requiresPhoto ?? this.requiresPhoto,
      confirmsAddress: confirmsAddress ?? this.confirmsAddress,
      applicableCountries: applicableCountries ?? this.applicableCountries,
      dataFields: dataFields ?? this.dataFields,
      notes: notes ?? this.notes,
      smartFiltering: smartFiltering ?? this.smartFiltering,
    );
  }

  List<DocumentDataField> get enhancedDataFields {
    return FieldTypeMappingService.enhanceDocumentFields(dataFields, key);
  }

  bool get hasEnhancedFields {
    return dataFields.any((field) =>
      FieldTypeMappingService.shouldUseEnhancedField(field.name, key)
    );
  }

  @override
  String toString() {
    return 'DocumentType(id: $id, name: $name, documentGroup: $documentGroup, requiresPhoto: $requiresPhoto, confirmsAddress: $confirmsAddress)';
  }
}

class DocumentDataField {
  final String name;
  final String type;
  final bool required;
  final String label;
  final String? placeholder;
  final List<String>? options;
  final Map<String, dynamic>? validationRules;
  final Map<String, dynamic>? uiConfig;
  final bool? conditional;
  final String? conditionalField;
  final dynamic conditionalValue;
  final String? helpText;

  DocumentDataField({
    required this.name,
    required this.type,
    required this.required,
    required this.label,
    this.placeholder,
    this.options,
    this.validationRules,
    this.uiConfig,
    this.conditional,
    this.conditionalField,
    this.conditionalValue,
    this.helpText,
  });

  bool get isEnhancedField => _enhancedFieldTypes.contains(type);

  static const List<String> _enhancedFieldTypes = [
    'yes_no_confirmation',
    'yes_no',
    'multiple_choice_postcode',
    'smart_date',
    'date',
    'country_dropdown',
    'dropdown',
    'enhanced_text',
    'string',
  ];

  String get enhancedType {
    return FieldTypeMappingService.getEnhancedFieldType(name, '');
  }

  factory DocumentDataField.fromJson(Map<String, dynamic> json) {
    List<String>? fieldOptions;

    // Handle different option types
    if (json['options'] != null) {
      if (json['options'] is List) {
        fieldOptions = List<String>.from(json['options'] as List);
      } else if (json['options'] is String) {
        // Handle special option types like 'countries_list', 'nationalities_list'
        fieldOptions = _getSpecialOptions(json['options'] as String);
      }
    }

    return DocumentDataField(
      name: json['field_name'] ?? json['name'] ?? '',
      type: json['field_type'] ?? json['type'] ?? 'string',
      required: json['required'] ?? false,
      label: json['label'] ?? '',
      placeholder: json['placeholder'],
      options: fieldOptions,
      validationRules: json['validation_rules'] != null && json['validation_rules'] is Map
          ? Map<String, dynamic>.from(json['validation_rules'])
          : null,
      uiConfig: json['ui_config'] != null && json['ui_config'] is Map
          ? Map<String, dynamic>.from(json['ui_config'])
          : null,
      conditional: json['conditional'] as bool?,
      conditionalField: json['conditional_field'] as String?,
      conditionalValue: json['conditional_value'],
      helpText: json['help_text'] as String?,
    );
  }

  static List<String> _getSpecialOptions(String optionType) {
    switch (optionType) {
      case 'countries_list':
        return [
          'United Kingdom', 'Ireland', 'France', 'Germany', 'Spain', 'Italy',
          'Netherlands', 'Belgium', 'Portugal', 'Poland', 'Czech Republic',
          'Hungary', 'Romania', 'Bulgaria', 'Croatia', 'Slovenia', 'Slovakia',
          'Estonia', 'Latvia', 'Lithuania', 'Finland', 'Sweden', 'Denmark',
          'Austria', 'Greece', 'Cyprus', 'Malta', 'Luxembourg', 'United States',
          'Canada', 'Australia', 'New Zealand', 'South Africa', 'India',
          'Pakistan', 'Bangladesh', 'Nigeria', 'Ghana', 'Kenya', 'Other'
        ];
      case 'countries_list_without_uk':
        return _getSpecialOptions('countries_list')
            .where((country) => !country.toLowerCase().contains('kingdom'))
            .toList();
      case 'nationalities_list':
        return [
          'British', 'Irish', 'French', 'German', 'Spanish', 'Italian',
          'Dutch', 'Belgian', 'Portuguese', 'Polish', 'Czech', 'Hungarian',
          'Romanian', 'Bulgarian', 'Croatian', 'Slovenian', 'Slovak',
          'Estonian', 'Latvian', 'Lithuanian', 'Finnish', 'Swedish', 'Danish',
          'Austrian', 'Greek', 'Cypriot', 'Maltese', 'Luxembourgish',
          'American', 'Canadian', 'Australian', 'New Zealand', 'South African',
          'Indian', 'Pakistani', 'Bangladeshi', 'Nigerian', 'Ghanaian',
          'Kenyan', 'Other'
        ];
      case 'eea_countries_list':
        return [
          'Austria', 'Belgium', 'Bulgaria', 'Croatia', 'Cyprus', 'Czech Republic',
          'Denmark', 'Estonia', 'Finland', 'France', 'Germany', 'Greece',
          'Hungary', 'Iceland', 'Ireland', 'Italy', 'Latvia', 'Liechtenstein',
          'Lithuania', 'Luxembourg', 'Malta', 'Netherlands', 'Norway',
          'Poland', 'Portugal', 'Romania', 'Slovakia', 'Slovenia', 'Spain',
          'Sweden'
        ];
      default:
        return [];
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'required': required,
      'label': label,
      if (placeholder != null) 'placeholder': placeholder,
      if (options != null) 'options': options,
      if (validationRules != null) 'validation_rules': validationRules,
      if (uiConfig != null) 'ui_config': uiConfig,
      if (conditional != null) 'conditional': conditional,
      if (conditionalField != null) 'conditional_field': conditionalField,
      if (conditionalValue != null) 'conditional_value': conditionalValue,
      if (helpText != null) 'help_text': helpText,
    };
  }

  bool shouldShow(Map<String, dynamic> formData) {
    if (conditional != true || conditionalField == null) return true;

    final fieldValue = formData[conditionalField];
    if (conditionalValue is bool) {
      return fieldValue == conditionalValue;
    } else if (conditionalValue is String) {
      return fieldValue?.toString().toLowerCase() == conditionalValue.toString().toLowerCase();
    }

    return fieldValue == conditionalValue;
  }

  DocumentDataField copyWith({
    String? name,
    String? type,
    bool? required,
    String? label,
    String? placeholder,
    List<String>? options,
    Map<String, dynamic>? validationRules,
    Map<String, dynamic>? uiConfig,
    bool? conditional,
    String? conditionalField,
    dynamic conditionalValue,
    String? helpText,
  }) {
    return DocumentDataField(
      name: name ?? this.name,
      type: type ?? this.type,
      required: required ?? this.required,
      label: label ?? this.label,
      placeholder: placeholder ?? this.placeholder,
      options: options ?? this.options,
      validationRules: validationRules ?? this.validationRules,
      uiConfig: uiConfig ?? this.uiConfig,
      conditional: conditional ?? this.conditional,
      conditionalField: conditionalField ?? this.conditionalField,
      conditionalValue: conditionalValue ?? this.conditionalValue,
      helpText: helpText ?? this.helpText,
    );
  }

  @override
  String toString() {
    return 'DocumentDataField(name: $name, type: $type, enhancedType: $enhancedType, required: $required, label: $label)';
  }
}
