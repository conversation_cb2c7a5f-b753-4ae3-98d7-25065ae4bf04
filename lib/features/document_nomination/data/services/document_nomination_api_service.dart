import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/network/api_exceptions.dart';
import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/data/models/route_requirement.dart';
import 'package:SolidCheck/features/document_nomination/data/models/validation_response.dart';
import 'package:SolidCheck/features/document_nomination/services/document_configuration_service.dart';
import 'package:dio/dio.dart';

class DocumentNominationApiService {
  late final Dio _dio;

  DocumentNominationApiService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseURL,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => print('🔵 Document Nomination API: $obj'),
      ),
    );
  }

  Future<AvailableDocumentsResponse> getAvailableDocuments(
    String token,
    String applicationId,
  ) async {
    try {
      // For now, use the comprehensive document configuration instead of API call
      // In production, this would make the actual API call and merge with comprehensive config

      final comprehensiveDocuments = await DocumentConfigurationService.loadAllDocuments();

      // Create mock response structure that matches the expected format
      final group1Docs = comprehensiveDocuments.where((doc) => doc.documentGroup == '1').toList();
      final group2aDocs = comprehensiveDocuments.where((doc) => doc.documentGroup == '2a').toList();
      final group2bDocs = comprehensiveDocuments.where((doc) => doc.documentGroup == '2b').toList();

      final mockResponse = <String, dynamic>{
        'success': true,
        'data': <String, dynamic>{
          'application': <String, dynamic>{
            'id': applicationId,
            'status': 'active',
          },
          'applicant_context': <String, dynamic>{
            'age': 25,
            'nationality': 'British',
            'address_countries': ['GB'],
            'has_uk_address': true,
          },
          'routing': <String, dynamic>{
            'recommended_route': 1,
            'available_routes': [1, 2],
            'route_requirements': <String, dynamic>{
              'route_1': <String, dynamic>{
                'name': 'Route 1 (Preferred)',
                'description': '1 Group 1 document + 1 additional document',
                'requirements': [
                  <String, dynamic>{'group': '1', 'count': 1},
                  <String, dynamic>{'group': 'any', 'count': 1}
                ]
              },
              'route_2': <String, dynamic>{
                'name': 'Route 2 (Fallback)',
                'description': '1 Group 2a document + 2 additional documents',
                'requirements': [
                  <String, dynamic>{'group': '2a', 'count': 1},
                  <String, dynamic>{'group': 'any', 'count': 2}
                ]
              }
            }
          },
          'documents': <String, dynamic>{
            'available_by_group': <String, dynamic>{
              '1': <String, dynamic>{
                'group_name': 'Group 1 - Primary Identity Documents',
                'document_count': group1Docs.length,
                'documents': group1Docs.map((doc) => doc.toJson()).toList(),
              },
              '2a': <String, dynamic>{
                'group_name': 'Group 2a - Trusted Government Documents',
                'document_count': group2aDocs.length,
                'documents': group2aDocs.map((doc) => doc.toJson()).toList(),
              },
              '2b': <String, dynamic>{
                'group_name': 'Group 2b - Financial and Social History Documents',
                'document_count': group2bDocs.length,
                'documents': group2bDocs.map((doc) => doc.toJson()).toList(),
              },
            },
            'total_available': comprehensiveDocuments.length,
            'current_nominations': <dynamic>[],
          }
        }
      };

      final parsedResponse = AvailableDocumentsResponse.fromJson(mockResponse);
      return parsedResponse;

    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to get available documents: ${error.toString()}');
    }
  }

  Future<ValidationResponse> validateDocumentNominations(
    String token,
    String applicationId,
    DocumentNominationRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '/applications/$applicationId/validate',
        data: request.toJson(),
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return ValidationResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to validate document nominations: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to validate document nominations: ${error.toString()}');
    }
  }

  Future<Map<String, dynamic>> getDocumentStatus(
    String token,
    String applicationId,
  ) async {
    try {
      final response = await _dio.get(
        '/applications/$applicationId/documents/status',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data['data'] as Map<String, dynamic>;
      } else {
        throw Exception('Failed to get document status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to get document status: ${error.toString()}');
    }
  }

  Future<Map<String, dynamic>> completeDocumentNomination(
    String token,
    String applicationId,
    Map<String, dynamic> requestData,
  ) async {
    try {
      final response = await _dio.post(
        '/applications/$applicationId/documents/complete',
        data: requestData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data['data'] as Map<String, dynamic>;
      } else {
        throw Exception('Failed to complete document nomination: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to complete document nomination: ${error.toString()}');
    }
  }

  Future<ValidationResponse> submitDocumentNominations(
    String token,
    String applicationId,
    DocumentNominationRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '/applications/$applicationId/documents/nominate',
        data: request.toJson(),
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200 || response.statusCode == 201) {
        return ValidationResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to submit document nominations: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to submit document nominations: ${error.toString()}');
    }
  }

  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return FetchDataException('Connection timeout');
      case DioExceptionType.badResponse:
        switch (e.response?.statusCode) {
          case 400:
            return BadRequestException(e.response?.data?.toString() ?? 'Bad request');
          case 401:
            return UnauthorisedException(e.response?.data?.toString() ?? 'Unauthorized');
          case 403:
            return UnauthorisedException('Access forbidden');
          case 404:
            return FetchDataException('Resource not found');
          case 422:
            return ValidationException(e.response?.data?.toString() ?? 'Validation error');
          case 500:
          default:
            return FetchDataException(
              'Server error: ${e.response?.statusCode ?? 'Unknown'}',
            );
        }
      case DioExceptionType.cancel:
        return FetchDataException('Request cancelled');
      case DioExceptionType.unknown:
      default:
        return FetchDataException('Network error: ${e.message}');
    }
  }
}
