import 'package:flutter/material.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/validation_result.dart';

class ValidationErrorDisplay extends StatelessWidget {
  final ValidationResult? validationResult;
  final EdgeInsetsGeometry? padding;
  final bool showWarnings;

  const ValidationErrorDisplay({
    super.key,
    this.validationResult,
    this.padding,
    this.showWarnings = true,
  });

  @override
  Widget build(BuildContext context) {
    if (validationResult == null || !validationResult!.hasIssues) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (validationResult!.hasErrors) ...[
            ...validationResult!.errors.map((error) => _buildErrorMessage(context, error)),
          ],
          if (showWarnings && validationResult!.hasWarnings) ...[
            if (validationResult!.hasErrors) const SizedBox(height: 4),
            ...validationResult!.warnings.map((warning) => _buildWarningMessage(context, warning)),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorMessage(BuildContext context, String message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.error_outline,
            color: Theme.of(context).colorScheme.error,
            size: 16,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningMessage(BuildContext context, String message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.warning_amber_outlined,
            color: Colors.orange[700],
            size: 16,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: Colors.orange[700],
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class InlineValidationField extends StatelessWidget {
  final Widget child;
  final ValidationResult? validationResult;
  final bool showWarnings;

  const InlineValidationField({
    super.key,
    required this.child,
    this.validationResult,
    this.showWarnings = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        child,
        ValidationErrorDisplay(
          validationResult: validationResult,
          showWarnings: showWarnings,
          padding: const EdgeInsets.only(top: 4.0, left: 12.0, right: 12.0),
        ),
      ],
    );
  }
}

class ValidationSummaryCard extends StatelessWidget {
  final List<ValidationResult> validationResults;
  final String title;
  final bool showWarnings;

  const ValidationSummaryCard({
    super.key,
    required this.validationResults,
    required this.title,
    this.showWarnings = true,
  });

  @override
  Widget build(BuildContext context) {
    final allErrors = validationResults
        .expand((result) => result.errors)
        .toList();
    
    final allWarnings = validationResults
        .expand((result) => result.warnings)
        .toList();

    if (allErrors.isEmpty && (!showWarnings || allWarnings.isEmpty)) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            if (allErrors.isNotEmpty) ...[
              Text(
                'Errors (${allErrors.length}):',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              ...allErrors.map((error) => _buildErrorItem(context, error)),
            ],
            if (showWarnings && allWarnings.isNotEmpty) ...[
              if (allErrors.isNotEmpty) const SizedBox(height: 16),
              Text(
                'Warnings (${allWarnings.length}):',
                style: TextStyle(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              ...allWarnings.map((warning) => _buildWarningItem(context, warning)),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorItem(BuildContext context, String message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 2.0),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.error,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningItem(BuildContext context, String message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 2.0),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.orange[700],
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: Colors.orange[700],
                fontSize: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ValidationStatusIcon extends StatelessWidget {
  final ValidationResult? validationResult;
  final double size;

  const ValidationStatusIcon({
    super.key,
    this.validationResult,
    this.size = 20,
  });

  @override
  Widget build(BuildContext context) {
    if (validationResult == null) {
      return SizedBox(width: size, height: size);
    }

    if (validationResult!.hasErrors) {
      return Icon(
        Icons.error,
        color: Theme.of(context).colorScheme.error,
        size: size,
      );
    }

    if (validationResult!.hasWarnings) {
      return Icon(
        Icons.warning_amber,
        color: Colors.orange[700],
        size: size,
      );
    }

    return Icon(
      Icons.check_circle,
      color: Colors.green[600],
      size: size,
    );
  }
}
