import 'package:flutter/material.dart';
import 'package:SolidCheck/features/document_nomination/services/validation/document_validation_service.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/validation_error_display.dart';

class ValidationTestWidget extends StatefulWidget {
  const ValidationTestWidget({super.key});

  @override
  State<ValidationTestWidget> createState() => _ValidationTestWidgetState();
}

class _ValidationTestWidgetState extends State<ValidationTestWidget> {
  final _licenceController = TextEditingController();
  String _validationOutput = '';

  void _testValidation() {
    final documentData = {
      'licence_number': _licenceController.text,
      'date_of_issue': '2020-01-01',
      'date_of_expiry': '2030-01-01',
      'country_region_of_issue': 'UK',
    };

    final applicantData = {
      'surname': '<PERSON>',
      'first_name': '<PERSON>',
      'middle_name': '<PERSON>',
      'date_of_birth': '1975-07-02',
      'gender': 'female',
    };

    final result = DocumentValidationService.validateDocumentData(
      documentKey: 'driving_licence_uk_photocard',
      documentData: documentData,
      applicantData: applicantData,
    );

    setState(() {
      _validationOutput = '''
Valid: ${result.isValid}
Errors: ${result.errors.join(', ')}
Warnings: ${result.warnings.join(', ')}
''';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Validation Test')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Test UK Driving Licence Validation'),
            const SizedBox(height: 16),
            TextField(
              controller: _licenceController,
              decoration: const InputDecoration(
                labelText: 'Licence Number',
                hintText: 'Enter ROBIN751025CJ9901 for valid test',
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _testValidation,
              child: const Text('Test Validation'),
            ),
            const SizedBox(height: 16),
            if (_validationOutput.isNotEmpty) ...[
              const Text('Validation Result:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(_validationOutput),
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _licenceController.dispose();
    super.dispose();
  }
}
