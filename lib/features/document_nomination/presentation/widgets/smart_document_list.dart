import 'package:flutter/material.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_applicant_context_service.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_document_filtering_service.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/smart_document_tile.dart';

class SmartDocumentList extends StatelessWidget {
  final List<DocumentType> documents;
  final ApplicantContext applicantContext;
  final List<String> selectedDocumentKeys;
  final Function(DocumentType) onDocumentSelected;
  final bool showPersonalizedGuidance;
  final bool groupByRecommendation;

  const SmartDocumentList({
    super.key,
    required this.documents,
    required this.applicantContext,
    required this.selectedDocumentKeys,
    required this.onDocumentSelected,
    this.showPersonalizedGuidance = true,
    this.groupByRecommendation = true,
  });

  @override
  Widget build(BuildContext context) {
    final availableDocuments = SmartDocumentFilteringService.filterAvailableDocuments(
      documents,
      applicantContext,
    );

    if (availableDocuments.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showPersonalizedGuidance) ...[
          _buildPersonalizedGuidance(context),
          const SizedBox(height: 16),
        ],
        if (groupByRecommendation)
          _buildGroupedDocuments(context, availableDocuments)
        else
          _buildSimpleList(context, availableDocuments),
      ],
    );
  }

  Widget _buildPersonalizedGuidance(BuildContext context) {
    final guidance = SmartDocumentFilteringService.getPersonalizedGuidance(applicantContext);
    final routeRecommendation = SmartDocumentFilteringService.getRouteRecommendation(applicantContext);

    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Personalized Guidance',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.route,
                    color: Colors.blue[700],
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      routeRecommendation,
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                        fontSize: 13,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            ...guidance.map((tip) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      tip,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[700],
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildGroupedDocuments(BuildContext context, List<DocumentType> availableDocuments) {
    final groupedDocuments = SmartDocumentFilteringService.groupDocumentsByRecommendation(
      availableDocuments,
      applicantContext,
    );

    return Column(
      children: groupedDocuments.entries.map((entry) {
        final groupName = entry.key;
        final groupDocuments = entry.value;
        
        return Column(
          children: [
            SmartDocumentGroupHeader(
              groupName: groupName,
              documentCount: groupDocuments.length,
              description: _getGroupDescription(groupName),
            ),
            ...groupDocuments.map((document) => SmartDocumentTile(
              documentType: document,
              applicantContext: applicantContext,
              isSelected: selectedDocumentKeys.contains(document.key),
              onTap: () => onDocumentSelected(document),
              showRecommendationBadge: false, // Already grouped by recommendation
            )),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildSimpleList(BuildContext context, List<DocumentType> availableDocuments) {
    final sortedDocuments = SmartDocumentFilteringService.sortDocumentsByRecommendation(
      availableDocuments,
      applicantContext,
    );

    return Column(
      children: sortedDocuments.map((document) => SmartDocumentTile(
        documentType: document,
        applicantContext: applicantContext,
        isSelected: selectedDocumentKeys.contains(document.key),
        onTap: () => onDocumentSelected(document),
      )).toList(),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.description_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Documents Available',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Based on your profile, no documents in this group are currently available. Please check other document groups or contact support if you believe this is an error.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String? _getGroupDescription(String groupName) {
    switch (groupName) {
      case 'Highly Recommended':
        return 'These documents are perfect for your situation and will make verification easier';
      case 'Recommended':
        return 'Good choices that work well for most applicants in your situation';
      case 'Available':
        return 'These documents can be used but may require additional verification';
      case 'Not Recommended':
        return 'These documents may be difficult to verify or have limitations';
      default:
        return null;
    }
  }
}

class SmartDocumentStats extends StatelessWidget {
  final List<DocumentType> allDocuments;
  final ApplicantContext applicantContext;

  const SmartDocumentStats({
    super.key,
    required this.allDocuments,
    required this.applicantContext,
  });

  @override
  Widget build(BuildContext context) {
    final availableCount = allDocuments.where((doc) => 
      SmartDocumentFilteringService.isDocumentAvailable(doc.key, applicantContext)
    ).length;
    
    final recommendedCount = allDocuments.where((doc) {
      final availability = SmartDocumentFilteringService.getDocumentAvailability(doc.key, applicantContext);
      return availability.recommendationLevel == DocumentRecommendationLevel.highly_recommended ||
             availability.recommendationLevel == DocumentRecommendationLevel.recommended;
    }).length;

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(0.1),
            Theme.of(context).colorScheme.primary.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              context,
              'Available',
              availableCount.toString(),
              Icons.description,
              Colors.blue,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.grey[300],
          ),
          Expanded(
            child: _buildStatItem(
              context,
              'Recommended',
              recommendedCount.toString(),
              Icons.star,
              Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
