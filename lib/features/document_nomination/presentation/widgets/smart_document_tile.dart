import 'package:flutter/material.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_document_filtering_service.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_applicant_context_service.dart';

class SmartDocumentTile extends StatelessWidget {
  final DocumentType documentType;
  final ApplicantContext applicantContext;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool showRecommendationBadge;
  final bool showInfoMessage;

  const SmartDocumentTile({
    super.key,
    required this.documentType,
    required this.applicantContext,
    required this.isSelected,
    this.onTap,
    this.showRecommendationBadge = true,
    this.showInfoMessage = true,
  });

  @override
  Widget build(BuildContext context) {
    final availability = SmartDocumentFilteringService.getDocumentAvailability(
      documentType.key,
      applicantContext,
    );

    if (!availability.isAvailable) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                documentType.name,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected 
                                    ? Theme.of(context).colorScheme.primary
                                    : null,
                                ),
                              ),
                            ),
                            if (showRecommendationBadge)
                              _buildRecommendationBadge(context, availability.recommendationLevel),
                          ],
                        ),
                        if (documentType.notes?.isNotEmpty == true) ...[
                          const SizedBox(height: 4),
                          Text(
                            documentType.notes!,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  _buildDocumentIcon(context),
                  const SizedBox(width: 8),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    )
                  else
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey[400]!,
                          width: 2,
                        ),
                      ),
                    ),
                ],
              ),
              if (showInfoMessage && availability.infoMessage != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getInfoMessageColor(context, availability.recommendationLevel).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getInfoMessageColor(context, availability.recommendationLevel).withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getInfoMessageIcon(availability.recommendationLevel),
                        size: 16,
                        color: _getInfoMessageColor(context, availability.recommendationLevel),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          availability.infoMessage!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: _getInfoMessageColor(context, availability.recommendationLevel),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              if (availability.reasons.isNotEmpty) ...[
                const SizedBox(height: 8),
                ...availability.reasons.map((reason) => Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 14,
                        color: Colors.orange[700],
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          reason,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.orange[700],
                            fontSize: 11,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecommendationBadge(BuildContext context, DocumentRecommendationLevel level) {
    Color badgeColor;
    String badgeText;
    IconData badgeIcon;

    switch (level) {
      case DocumentRecommendationLevel.highly_recommended:
        badgeColor = Colors.green;
        badgeText = 'RECOMMENDED';
        badgeIcon = Icons.star;
        break;
      case DocumentRecommendationLevel.recommended:
        badgeColor = Colors.blue;
        badgeText = 'GOOD CHOICE';
        badgeIcon = Icons.thumb_up;
        break;
      case DocumentRecommendationLevel.available:
        return const SizedBox.shrink();
      case DocumentRecommendationLevel.not_recommended:
        badgeColor = Colors.orange;
        badgeText = 'LIMITED';
        badgeIcon = Icons.warning;
        break;
      case DocumentRecommendationLevel.unavailable:
        return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            badgeIcon,
            size: 12,
            color: badgeColor,
          ),
          const SizedBox(width: 4),
          Text(
            badgeText,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: badgeColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentIcon(BuildContext context) {
    IconData iconData;
    Color iconColor = Colors.grey[600]!;

    if (documentType.requiresPhoto) {
      iconData = Icons.photo_camera;
      iconColor = Colors.blue[600]!;
    } else if (documentType.confirmsAddress) {
      iconData = Icons.home;
      iconColor = Colors.green[600]!;
    } else {
      iconData = Icons.description;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        size: 20,
        color: iconColor,
      ),
    );
  }

  Color _getInfoMessageColor(BuildContext context, DocumentRecommendationLevel level) {
    switch (level) {
      case DocumentRecommendationLevel.highly_recommended:
        return Colors.green[700]!;
      case DocumentRecommendationLevel.recommended:
        return Colors.blue[700]!;
      case DocumentRecommendationLevel.available:
        return Colors.grey[700]!;
      case DocumentRecommendationLevel.not_recommended:
        return Colors.orange[700]!;
      case DocumentRecommendationLevel.unavailable:
        return Colors.red[700]!;
    }
  }

  IconData _getInfoMessageIcon(DocumentRecommendationLevel level) {
    switch (level) {
      case DocumentRecommendationLevel.highly_recommended:
        return Icons.check_circle_outline;
      case DocumentRecommendationLevel.recommended:
        return Icons.info_outline;
      case DocumentRecommendationLevel.available:
        return Icons.info_outline;
      case DocumentRecommendationLevel.not_recommended:
        return Icons.warning_amber_outlined;
      case DocumentRecommendationLevel.unavailable:
        return Icons.error_outline;
    }
  }
}

class SmartDocumentGroupHeader extends StatelessWidget {
  final String groupName;
  final int documentCount;
  final String? description;

  const SmartDocumentGroupHeader({
    super.key,
    required this.groupName,
    required this.documentCount,
    this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      margin: const EdgeInsets.only(top: 16, bottom: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  groupName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$documentCount available',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          if (description != null) ...[
            const SizedBox(height: 4),
            Text(
              description!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.8),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
